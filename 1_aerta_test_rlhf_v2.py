from pettingzoo.userhfmappo import aerta_position_V12
from pettingzoo.userhfmappo import aerta_helicopter_bestfit_free
from pettingzoo.userhfmappo import aerta_helicopter_nearby_free
# from pettingzoo.userhfmappo import aerta_helicopter_nearby_free,aerta_helicopter_nearby_all,aerta_helicopter_timesati_free,aerta_helicopter_timesati_all
from pettingzoo.userhfmappo.aerta_position_V12 import Posi<PERSON>,<PERSON>licop<PERSON>,Task
from pettingzoo.test import api_test
from stable_baselines3 import PPO
import supersuit as ss
from pettingzoo.utils import to_parallel
import pandas as pd
from stable_baselines3.common.evaluation import evaluate_policy
import matplotlib.pyplot as plt
from stable_baselines3.common.callbacks import BaseCallback
from stable_baselines3.common.callbacks import EvalCallback
import numpy as np
import random
from reward_model import rewardModel
import torch as th
from api import run_llm_local
#from task_alloc import TaskAllocation

positions=[Position(0,"萧山机场",1),Position(1,"杭州市富阳区",1),
        Position(2,"杭州市桐庐县",0),Position(3,"丽水市缙云县",0),Position(4,"台州市临海市",1),
        Position(5,"台州市仙居县",0),Position(6,"台州市天台县",0),Position(7,"台州市三门县",0),
        Position(8,"舟山市嵊泗县",0),Position(9,"舟山市岱山县",0),Position(10,"舟山市定海区",1),
        Position(11,"金华市东阳市",1),Position(12,"金华市义乌市",1),Position(13,"金华市磐安县",0),
        Position(14,"金华市浦江县",0),Position(15,"绍兴市嵊州市",1),Position(16,"绍兴市诸暨市",1),
        Position(17,"绍兴市新昌县",0),Position(18,"绍兴市上虞区",1),Position(19,"绍兴市柯桥区",1),
        Position(20,"宁波市余姚市",1),Position(21,"宁波市宁海县",0),Position(22,"宁波市象山县",0),
        Position(23,"宁波市奉化区",1),Position(24,"宁波市鄞州区",1),Position(25,"宁波市北仑区",1),
        Position(26,"宁波市海曙区",1)]
edge=[
            [0,57,87,177,166,160,137,178,201,166,159,113,99,113,98,81,66,96,48,32,74,133,156,100,125,140,100],
            [57,0,34,132,154,133,128,181,252,211,198,75,54,101,43,82,21,97,81,48,109,144,176,120,152,174,126],
            [87,34,0,131,174,148,152,203,285,245,232,86,63,114,29,111,46,125,115,82,143,173,207,153,185,207,159],
            [177,132,131,0,104,64,106,152,321,270,245,64,79,55,93,123,113,122,165,148,179,153,188,160,188,215,172],
            [166,154,174,104,0,41,29,48,241,187,159,91,113,68,140,85,133,71,129,135,125,65,92,92,106,131,104],
            [160,133,148,64,41,0,47,89,270,216,189,62,85,34,111,86,112,77,133,128,138,94,128,111,134,159,123],
            [137,128,152,106,29,47,0,56,224,169,142,74,94,57,121,56,108,42,100,107,98,48,82,66,87,113,79],
            [178,181,203,152,48,89,56,0,208,154,124,131,150,111,176,101,162,85,133,150,120,49,55,83,82,100,91],
            [201,252,285,321,241,270,223,208,0,55,84,267,268,268,282,202,250,200,173,205,149,177,152,162,136,111,150],
            [166,211,245,270,187,216,170,154,55,0,30,217,222,215,238,152,205,149,130,163,103,123,98,111,83,56,98],
            [159,198,232,245,159,189,142,124,84,30,0,194,201,191,221,132,190,126,118,150,89,95,59,88,57,30,77],
            [113,75,86,64,92,62,74,131,267,217,194,0,23,27,49,65,54,69,102,85,120,112,149,107,139,166,119],
            [99,54,63,79,113,85,94,150,268,222,201,23,0,50,27,69,35,79,98,73,119,124,161,114,148,173,125],
            [113,101,114,55,58,34,57,111,267,215,191,27,50,0,76,69,80,68,114,103,125,101,138,105,135,162,118],
            [98,43,29,93,140,111,121,176,282,238,221,49,27,76,0,91,34,102,109,80,134,149,185,135,168,193,144],
            [81,82,111,123,85,86,56,101,202,152,132,65,69,69,91,0,66,16,46,50,56,62,96,44,78,104,55],
            [66,21,46,113,133,112,108,162,250,205,190,54,35,80,34,66,0,80,77,46,102,127,162,108,140,164,116],
            [96,97,125,122,71,77,42,85,200,149,126,69,79,68,102,16,80,0,58,66,60,48,84,39,71,98,51],
            [48,82,115,165,129,133,100,133,173,130,117,102,98,114,109,46,77,57,0,33,29,86,108,53,77,95,52],
            [32,48,82,148,135,128,107,150,205,163,150,85,73,103,80,50,46,66,33,0,61,107,135,79,107,128,81],
            [74,109,143,179,125,138,98,120,150,103,90,119,119,125,134,56,102,60,29,61,0,71,85,35,51,67,29],
            [133,144,173,153,65,95,48,49,177,123,95,112,124,101,149,62,127,48,86,107,71,0,36,36,41,65,43],
            [156,176,207,188,92,128,82,55,152,98,69,149,161,137,185,96,162,84,108,136,85,36,0,57,36,45,57],
            [100,120,153,160,92,111,66,83,162,111,88,107,114,105,135,44,108,39,53,79,35,36,57,0,33,59,13],
            [125,152,185,189,106,134,88,82,136,83,57,139,148,135,169,78,140,71,77,107,51,41,36,33,0,27,26],
            [140,174,207,215,131,159,113,100,111,56,30,166,173,162,193,104,164,98,95,128,67,65,45,59,27,0,49],
            [100,126,159,172,104,123,79,91,150,98,77,119,125,118,144,55,116,51,52,81,29,43,57,13,26,49,0]
        ]
helicopters=[Helicopter(1,'EC145',0,1,0,1793,3,2,680,246,0),
        Helicopter(2,'Bell412',0,1,1,1408,14,0,403,269,0),
        Helicopter(3,'Bell429',0,0,0,1371,8,0,761,285,0),
        Helicopter(4,'AW139',0,1,0,2778,13,2,568,290,0),
        Helicopter(5,'S-76',0,1,1,2129,10,2,639,269,0),
        Helicopter(6,'EC225',0,0,0,5744,20,4,857,260,0),
        Helicopter(7,'AW119Kx',0,1,0,1600,0,2,954,244,0),
        Helicopter(8,'AW109SP',0,1,1,1515,8,0,889,289,0),
        Helicopter(9,'H130',0,0,0,1050,8,0,617,240,0),
        Helicopter(10,'H135',0,0,1,1455,5,2,635,254,0),
        Helicopter(11,'H145',0,0,1,1793,8,1,680,246,0),
        Helicopter(12,'AS350B3',0,0,1,1018,6,0,652,235,0),
        Helicopter(13,'超黄蜂',0,1,1,3000,10,5,700,220,0)]
positions[0].helicopters=[1,2,3,4,5,6,7,8,9,10,11,12,13]
helicopters_v1=[Helicopter(0,'EC145',0,1,0,1793,3,2,680,246,0),
        Helicopter(1,'Bell412',0,1,1,1408,14,0,403,269,0),
        Helicopter(2,'Bell429',0,0,0,1371,8,0,761,285,0),
        Helicopter(3,'AW139',0,1,0,2778,13,2,568,290,0),
        Helicopter(4,'S-76',0,1,1,2129,10,2,639,269,0),
        Helicopter(5,'EC225',0,0,0,5744,20,4,857,260,0),
        Helicopter(6,'AW119Kx',0,1,0,1600,0,2,954,244,0),
        Helicopter(7,'AW109SP',0,1,1,1515,8,0,889,289,0),
        Helicopter(8,'H130',0,0,0,1050,8,0,617,240,0),
        Helicopter(9,'H135',0,0,1,1455,5,2,635,254,0),
        Helicopter(10,'H145',0,0,1,1793,8,1,680,246,0),
        Helicopter(11,'AS350B3',0,0,1,1018,6,0,652,235,0),
        Helicopter(12,'超黄蜂',0,1,1,3000,10,5,700,220,0)]

class TensorboardCallback(BaseCallback):
    """
    Custom callback for plotting additional values in tensorboard.
    """
    def __init__(self, n_steps,verbose=0):
        super(TensorboardCallback, self).__init__(verbose)
        self.n_steps = n_steps
        self.last_time_trigger = 0
        self.re=[]
        self.aver=[]
        self.aver_e=[]

    def _on_step(self) -> bool:
        # Log scalar value (here a random variable)
        # episode_rewards, episode_lengths = evaluate_policy(
        #         self.model,
        #         self.eval_env,
        #         n_eval_episodes=165888,
        #         return_episode_rewards=True,
        #         render=False
        #     )
        # mean_reward, std_reward = np.mean(episode_rewards), np.std(episode_rewards)
        # mean_ep_length, std_ep_length = np.mean(episode_lengths), np.std(episode_lengths)
        # self.logger.record("mean_reward", float(mean_reward))
        # self.logger.record("mean_ep_length", mean_ep_length)
        # self.logger.record('random_value', value)
        # self.logger.record("episode_rewards",float(episode_rewards))
        if (self.num_timesteps - self.last_time_trigger) >= self.n_steps:
            r=[]
            self.last_time_trigger = self.num_timesteps
            positions[0].helicopters=[1,2,3,4,5,6,7,8,9,10,11,12,13]
            env = aerta_position_V12.env(positions=positions,helicopters=helicopters,tasks=tasks,edge=edge)
            env.reset()
            for agent in env.agent_iter():
                obs, rew, done, info = env.last()
                if done==True:
                    break
                act = self.model.predict(obs, deterministic=True)[0] if not done else None
                env.step(act)
                r.append(env.render())
            env.close()
            self.re.append(r[-2][0])
            self.aver.append(r[-2][1])
            self.aver_e.append(r[-2][2])
            # self.logger.record('episode_reward', np.mean(self.re))
            # self.logger.record('average_time_satisfaction',np.mean(self.aver))
            # self.logger.record('variance',np.mean(self.aver_e))
            self.logger.record('episode_reward', self.re[-1])
            self.logger.record('average_time_satisfaction',self.aver[-1])
            self.logger.record('variance',self.aver_e[-1])
            # if len(self.re)==16:
            #     self.re=[]
            #     self.aver=[]
            #     self.aver_e=[]
        return True

def test():
    env = aerta_position_V12.env(positions=positions,helicopters=helicopters,tasks=tasks,edge=edge)
    api_test(env, num_cycles=100, verbose_progress=True)

def train():
    env = aerta_position_V12.env(positions=positions,helicopters=helicopters,tasks=tasks,edge=edge)
    # env = aerta_position_V12_plus.env(positions=positions,helicopters=helicopters,tasks=tasks,edge=edge)
    env = to_parallel(env)
    env = ss.pad_action_space_v0(env)
    env = ss.pettingzoo_env_to_vec_env_v1(env)
    env = ss.concat_vec_envs_v1(env, 48,base_class='stable_baselines3')
    logdir = './log/newobs_newrew/'
    model = PPO("MlpPolicy",env, batch_size=3456,verbose=3, gamma=0.95, n_steps=512, ent_coef=0.0905168, learning_rate=0.00062211, vf_coef=0.042202, max_grad_norm=0.9, gae_lambda=0.99, tensorboard_log=logdir)
    # model.learn(total_timesteps=15e6,tb_log_name='nohf_7685_15e6_v2',callback=TensorboardCallback(663552))
    model.learn(total_timesteps=15e6,tb_log_name='nohf_7685_15e6_v2')
    # model.learn(total_timesteps=1e7)
    model.save("./policy/newobs_newrew/nohf_7685_15e6_v2")
    env.close()

def train_again():
    env = aerta_position_V12.env(positions=positions,helicopters=helicopters,tasks=tasks,edge=edge)
    env = to_parallel(env)
    env = ss.pad_action_space_v0(env)
    env = ss.pettingzoo_env_to_vec_env_v1(env)
    env = ss.concat_vec_envs_v1(env, 48,base_class='stable_baselines3')
    logdir = './log/rmtrain/'
    model = PPO.load("./policy/newobs_newrew/nohf_7685_15e6",env=env)
    model.learn(total_timesteps=5e6,tb_log_name='rmtrain/rlhf_7685_15e6_7685_1e6_v3')
    # model.learn(total_timesteps=1e7)
    model.save("./policy/rmtrain/rlhf_7685_15e6_7685_5e6_v3")
    env.close()

#微调
def finetune():
    env = aerta_position_V12.env(positions=positions,helicopters=helicopters,tasks=tasks,edge=edge)
    env = to_parallel(env)
    env = ss.pad_action_space_v0(env)
    env = ss.pettingzoo_env_to_vec_env_v1(env)
    env = ss.concat_vec_envs_v1(env, 48,base_class='stable_baselines3')
    logdir = './log/rmtrain/'
    model = PPO.load("./policy/newobs_newrew/nohf_7685_15e6",env=env)
    model_ref = PPO.load("./policy/newobs_newrew/nohf_7685_15e6",env=env)
    model.finetune(total_timesteps=5e5,model_ref=model_ref,tb_log_name='rmtrain/rlhf_7685_15e6_7685_1e6_v3')
    model.save("./policy/rmtrain/rlhf_7685_15e6_110_5e5_v4")

def random_policy(obs,env):
    action_space=env.action_spaces[env.agent_selection]
    return action_space.sample()

def nearby_policy(env):
    return 1

#task_allocation=TaskAllocation()
def rl_policy(obs,env,info):
    #model = PPO.load("./policy/rmtrain/rlhf_7685_15e6_7685_5e5_v4_379")
    model = PPO.load("nohf_7685_15e6_v2")
    # model = PPO.load("./policy/aerta_position_V10_PPO_manuscript7")
    # model = PPO.load("D:\\00marl\\1小论文\\policy\\aerta_position_V11_PPO_6_self_12")
    return model.predict(obs, deterministic=True)[0]
    # m = rewardModel(880,1)
    # state_dict = th.load('D:\\00marl\\2大论文\\reward_model_state_dict_best.pth')
    # m.load_state_dict(state_dict)
    # m.eval()
    # action1 = model.predict(obs, deterministic=True)[0]
    # action2 = model.predict(obs, deterministic=False)[0]
    # log = model.policy.get_distribution(model.policy.obs_to_tensor(obs)[0]).log_prob(th.tensor(action1))[0]
    # if info=={}:
    #     return action1
    # if np.exp(log.detach().numpy())>=0.09:
    #     return action1

    # combined_context = th.cat([
    #                         th.tensor(obs, dtype=th.float32).view(-1),
    #                         th.tensor([[row[0]]+row[2:] for row in info['myself'][0]], dtype=th.float32).view(-1),
    #                         th.tensor(info['myself'][1], dtype=th.float32).view(-1),
    #                         th.tensor(info['myself'][2], dtype=th.float32).view(-1),
    #                         th.tensor(info['myself'][3], dtype=th.float32).view(-1)
    #                     ])
    # max_length=880
    # padding_size = max_length - combined_context.size(0)
    # if padding_size > 0:
    #     padded_tensor = th.nn.functional.pad(combined_context, (0, padding_size), "constant", 0)
    # else:
    #     padded_tensor = combined_context
    # padded_tensor = padded_tensor.unsqueeze(0)

    # action_tensor = th.tensor([action1], dtype=th.float32).unsqueeze(0)
    # action_tensor_comp = th.tensor([action2], dtype=th.float32).unsqueeze(0)

    # with th.no_grad():
    #     output1 = m(padded_tensor, action_tensor)
    #     output2 = m(padded_tensor, action_tensor_comp)
    # if output1<output2:
    #     return action2
    # return action1

    # llm feedback
    # if info=={}:
    #     return model.predict(obs, deterministic=True)[0]
    # prompt = task_allocation.generate_keywords_nocontrast(obs,info['myself'])
    # if '你没有有资格派遣的直升机组' in prompt:
    #     return 0
    # response = task_allocation.get_response(prompt)
    # if '理由' in response.split('\n')[1]:
    #     action = int(response.split('\n')[0].split('：')[1])
    # else:
    #     action = int(response.split('\n')[1])
    # if action==0:
    #     return action
    # else:
    #     print(action)
    #     for j in range(14):
    #         if obs[0][j][0]==action:
    #             action=j
    #             break
    # return action

def main_rl():
    positions[0].helicopters=[1,2,3,4,5,6,7,8,9,10,11,12,13]
    env = aerta_position_V12.env(positions=positions,helicopters=helicopters,tasks=tasks,edge=edge)
    # env = aerta_position_V12_plus.env(positions=positions,helicopters=helicopters,tasks=tasks,edge=edge)
    env.reset()
    value=[]
    clock=[]
    for agent in env.agent_iter():
        obs, rew, terminations, info = env.last()
        if terminations==True:
            break
        act = rl_policy(obs,env,info) if not terminations else None
        env.step(act)
        v,t=env.render()
        value.append(v)
        clock.append(t)
    env.close()
    return value,clock

def main_bestfit():
    positions[0].helicopters=[0,1,2,3,4,5,6,7,8,9,10,11,12]
    env = aerta_helicopter_bestfit_free.env(positions=positions,helicopters=helicopters_v1,tasks=tasks,edge=edge)
    env.reset()
    value=[]
    clock=[]
    for agent in env.agent_iter():
        obs, rew, done, info = env.last()
        if done==True:
            break
        act = 1 if not done else None  # 始终报名
        env.step(act)
        v,t=env.render()
        value.append(v)
        clock.append(t)
    env.close()
    return value,clock

def main_ny():
    positions[0].helicopters=[0,1,2,3,4,5,6,7,8,9,10,11,12]
    env = aerta_helicopter_nearby_free.env(positions=positions,helicopters=helicopters_v1,tasks=tasks,edge=edge)
    env.reset()
    value=[]
    clock=[]
    for agent in env.agent_iter():
        obs, rew, done, info = env.last()
        if done==True:
            break
        act = nearby_policy(env) if not done else None
        env.step(act)
        v,t=env.render()
        value.append(v)
        clock.append(t)
        # plt.plot(clock, value, color='orange', label='Proximity strategy')
        # plt.pause(0.01)
    env.close()
    return value,clock

    
def generate_tasks(seed, total_tasks, max_tasks_per_time, periods, start_clock=1, start_number=1):
    random.seed(seed)
    new_tasks = []
    tasks_count_per_time = {}  # Track the number of tasks at each publish_clock

    number=start_number
    while len(new_tasks) < total_tasks:
        publish_clock = random.randint(1, periods) + start_clock - 1
        pos=random.randint(1,26)
        search=0
        material=0
        person=0
        medical=0
        type=random.randint(1,4)
        if type==1:
            search=1
        elif type==2:
            material=random.randint(1,600)
        elif type==3:
            person=random.randint(1,10)
        elif type==4:
            medical=random.randint(1,5)
        if (search+material+person+medical)==0:
            search=1
        a=random.randint(10,100)
        b=a+random.randint(0,30)
        c=b+random.randint(0,30)
        d=random.random()
        m=[a,b,c,d]
        task=Task(number,publish_clock,0,0,pos,search,material,person,medical,m)
        
        # Ensure task count per time slot does not exceed the maximum
        while tasks_count_per_time.get(publish_clock, 0) >= max_tasks_per_time:
            publish_clock = random.randint(1, periods) + start_clock - 1

        new_tasks.append(task)
        tasks_count_per_time[publish_clock] = tasks_count_per_time.get(publish_clock, 0) + 1
        number+=1

    new_tasks.sort(key=lambda task: task.publish_clock)
    return new_tasks

def generate_base_data(seed=86,total_tasks_per_set=20,max_tasks_per_time=3,periods_per_set=10,num_datasets=10):
    task_sets = []
    last_clock = 0  # Initial start clock
    last_number = 1
    for i in range(num_datasets):
        new_tasks = generate_tasks(seed + i, total_tasks_per_set, max_tasks_per_time, periods_per_set, start_clock=last_clock ,start_number = last_number)
        task_sets.extend(new_tasks)
        last_clock = new_tasks[-1].publish_clock + 170  # Update start_clock based on the last publish_clock plus a buffer
        last_number = last_number + total_tasks_per_set
    return task_sets

if __name__ == '__main__':
    # csv_file = './data/聚集性任务.csv'
    # data = pd.read_csv(csv_file,encoding = 'gb2312')
    # tasks=[]
    # total_deliver=0
    # for i in range(len(data)):
    #     no=data.iloc[i,0]
    #     publish_clock=data.iloc[i,1]
    #     pos=data.iloc[i,2]
    #     search=data.iloc[i,3]
    #     material=data.iloc[i,4]
    #     person=data.iloc[i,5]
    #     medical=data.iloc[i,6]
    #     deliver=search+material/100+person+medical
    #     total_deliver+=deliver
    #     m=[]
    #     m.append(data.iloc[i,7])
    #     m.append(data.iloc[i,8])
    #     m.append(data.iloc[i,9])
    #     m.append(data.iloc[i,10])
    #     tasks.append(Task(no,publish_clock,0,0,pos,search,material,person,medical,m))
    tasks=generate_base_data()

    #test()
    # train()
    # train_again()
    #value_ny,clock_ny=main_ny()
    #value_bf,clock_bf=main_bestfit()
    # finetune()
    main_rl()

    # plt.figure(dpi=500)
    # plt.xlabel('Time')
    # plt.ylabel('Total benefit')
    # plt.plot(clock_rl,value_rl,label='Training strategy')
    # plt.plot(clock_ny,value_ny,label='Proximity strategy')
    # plt.legend()
    # plt.savefig("D:\onedrive\\1大论文\\2绘图\\1010_nohf.tif")
    # plt.show()

    
    # 将数组转换成pandas DataFrame
    # df = pd.DataFrame({
    #     'Value_rl': value_rl,
    #     'Clock_rl': clock_rl
    # })

    # # 将DataFrame保存为CSV文件
    # df.to_csv('D:\onedrive\\1大论文\\2绘图\\value\\1010_llm.csv', index=False)  # 如果不想保存行索引，设置index=False

    # 将数组转换成pandas DataFrame
    # df1 = pd.DataFrame({
    #     'Value_ny': value_ny,
    #     'Clock_ny': clock_ny
    # })

    # # 将DataFrame保存为CSV文件
    # df1.to_csv('D:\onedrive\\1大论文\\2绘图\\value\\1010_ny.csv', index=False)  # 如果不想保存行索引，设置index=False