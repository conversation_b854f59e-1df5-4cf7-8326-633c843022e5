# 直升机任务分配强化学习系统 (RLAIF) 产品需求文档

## 项目概述

本项目旨在开发一个基于强化学习和人工智能反馈（RLAIF）的直升机任务分配系统。系统通过多智能体强化学习来优化应急救援场景下的直升机调度，同时集成奖励模型来提升决策质量。

## 核心目标

### 主要功能
1. **多智能体强化学习环境**：基于PettingZoo框架构建的直升机任务分配环境
2. **奖励模型训练**：使用人类反馈数据训练神经网络奖励模型
3. **策略优化**：通过PPO算法训练智能体进行最优任务分配
4. **性能评估**：对比启发式策略、基础RL和RLAIF增强策略的性能
5. **代码迁移**：从Python 3.7/旧版本迁移到现代技术栈

### 技术栈
- **强化学习**：Stable-Baselines3 >= 2.0.0, PPO算法
- **环境框架**：PettingZoo >= 1.24.0, Gymnasium >= 0.29.1  
- **深度学习**：PyTorch >= 2.3.0
- **数据处理**：NumPy, Pandas, Scikit-learn
- **可视化**：Matplotlib, TensorBoard

## 详细需求

### 1. 环境和数据结构
- **位置(Position)**：27个地理位置，包括机场和任务点
- **直升机(Helicopter)**：13种不同型号，具有搜救、运输、医疗能力
- **任务(Task)**：动态生成的应急任务，包含时间敏感性和满意度函数
- **距离矩阵**：27x27的位置间距离/时间成本矩阵

### 2. 强化学习环境（aerta_position_v13_new.py）
- 多智能体AEC环境，每个位置为一个智能体
- 观察空间：2x(直升机数+1)x7的多维数组
- 动作空间：选择分配给任务的直升机（离散空间）
- 奖励函数：基于任务完成的时间满意度和需求满足度

### 3. 奖励模型（reward_model.py）
- **输入**：上下文信息（观察、智能体信息）+ 响应动作
- **架构**：全连接神经网络，包含BatchNorm和Dropout
- **训练**：使用偏好数据进行二分类，支持K折交叉验证
- **输出**：动作-状态对的奖励分数

### 4. 训练脚本（train_v13_simple.py）
- 支持新版本API的PPO训练流程
- 环境包装：pad_action_space, pettingzoo_env_to_vec_env等
- 模型保存和加载功能
- 简化的测试和验证流程

### 5. 评估系统（evaluate_all_strategies_new.py）
- **启发式策略**：临近策略(Nearby)、最优匹配策略(Best-fit)
- **强化学习策略**：基础RL、LLM调优RL、奖励模型调优RL
- **指标**：总效益、失败任务数、与最佳策略的差距
- **批量评估**：多种子、多轮次的性能对比

### 6. 代码迁移（MIGRATION_GUIDE.md）
- 从Python 3.7迁移到3.9+
- Gym -> Gymnasium的API更新
- Stable-Baselines3版本升级和超参数调整
- PettingZoo环境的兼容性处理

## 性能要求

### 训练性能
- 支持GPU加速训练
- 早停机制防止过拟合
- 模型检查点保存和恢复

### 评估性能
- 多策略并行评估能力
- 结果日志记录和可视化
- 统计分析（均值、标准差、置信区间）

### 系统性能
- 内存效率：支持大规模任务集处理
- 可扩展性：支持不同规模的直升机队伍和任务数量
- 可重现性：固定随机种子保证结果一致性

## 数据要求

### 训练数据
- **反馈数据**：feedback_data08.json，包含上下文-动作-偏好三元组
- **任务数据**：动态生成，可配置任务数量、时间分布、需求类型
- **地理数据**：位置坐标、距离矩阵、起降能力

### 模型文件
- **基础模型**：nohf_7685_15e6（无人类反馈的基础RL模型）
- **RLAIF模型**：rlhf_*系列（集成奖励模型的增强策略）
- **奖励模型**：reward_model_*.pth（训练好的奖励网络）

## 验收标准

### 功能完整性
- [x] 新版本API环境正常运行
- [x] 奖励模型训练收敛
- [x] PPO训练稳定进行
- [x] 多策略评估系统工作正常

### 性能指标
- RLAIF策略相比基础RL提升10%以上的平均效益
- 失败任务率控制在5%以下
- 训练时间在合理范围内（<24小时）

### 代码质量
- 支持新版本依赖（requirements_new.txt）
- 代码结构清晰，注释完整
- 错误处理和日志记录完善
- 可重现的实验结果

## 交付物

1. **核心代码**
   - 更新的环境实现（v13版本）
   - 奖励模型训练脚本
   - PPO训练脚本
   - 综合评估脚本

2. **模型文件**
   - 训练好的强化学习策略
   - 校准的奖励模型
   - 性能基准模型

3. **文档**
   - 迁移指南
   - 实验结果报告
   - 使用说明文档

4. **评估报告**
   - 多策略性能对比
   - 统计分析结果
   - 可视化图表

## 里程碑

1. **阶段1**：环境迁移和基础功能验证
2. **阶段2**：奖励模型开发和训练
3. **阶段3**：RLAIF策略训练和优化
4. **阶段4**：综合评估和性能分析
5. **阶段5**：文档完善和代码清理

本项目将为应急救援领域提供先进的AI决策支持系统，通过强化学习和人类反馈的结合，实现高效的资源分配和任务调度。