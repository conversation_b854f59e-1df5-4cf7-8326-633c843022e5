absl-py==1.4.0
annotated-types ==0.5.0
anyio== 3.7.1
cached-property ==1.5.2
cachetools==5.3.0
certifi== 2022.12.7  
cffi==1.15.1
charset-normalizer==3.1.0
cloudpickle== 1.6.0
colorama==0.4.6
cycler== 0.11.0
distro==  1.9.0
exceptiongroup==1.2.0
fonttools== 4.38.0
google-auth==   2.16.2
google-auth-oauthlib==0.4.6
grpcio==  1.51.3
gym
gym-notices==  0.0.8
h11==0.14.0
httpcore==0.17.3
httpx== 0.24.1
idna==3.4
imbalanced-learn== 0.7.0
importlib-metadata==  6.0.0
joblib==  1.3.2
kiwisolver==1.4.4
Markdown==3.4.1
MarkupSafe==2.1.2
matplotlib==3.5.3
numpy==   1.21.4
oauthlib==3.2.2
openai==  1.12.0
opencv-python== *********
packaging== 23.0
pandas==  1.3.5
PettingZoo==1.14.0
Pillow==  9.4.0
pip== 22.3.1
protobuf==3.20.3
pyasn1==  0.4.8
pyasn1-modules==0.2.8
pycparser== 2.21
pydantic==2.5.3
pydantic_core== 2.14.6
pygame==  2.1.2
PyJWT==   2.8.0
pymunk==  6.2.1
pyparsing==3.0.9
python-dateutil ==2.8.2
pytz==2022.7.1
requests==2.28.2
requests-oauthlib== 1.3.1
rsa== 4.9
scikit-learn==  1.0.2
scipy==   1.7.3
setuptools==65.6.3
six==1.16.0
sniffio== 1.3.1
stable-baselines3
SuperSuit== 3.3.2
tensorboard==   2.11.2
tensorboard-data-server== 0.6.1
tensorboard-plugin-wit ==1.8.1
threadpoolctl== 3.1.0
torch==   1.13.1
tqdm==4.66.2
typing_extensions==   4.7.1
urllib3== 1.26.15
Werkzeug==2.2.3
wheel==   0.37.1
wincertstore==  0.2
zhipuai== 2.0.1.20240423.1
zipp==3.12.1