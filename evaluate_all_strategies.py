import numpy as np
import random
import copy
import datetime
import os
import sys

# 从pettingzoo环境导入必要的类和函数
import aerta_helicopter_bestfit_free
import aerta_helicopter_nearby_free
import aerta_position_V12
from aerta_position_V12 import Position, He<PERSON>opter, Task
from stable_baselines3 import PPO

# ==============================================================================
# 核心数据定义 (从 1_aerta_test_rlhf_v2.py 复制)
# ==============================================================================

positions=[Position(0,"萧山机场",1),Position(1,"杭州市富阳区",1),
        Position(2,"杭州市桐庐县",0),Position(3,"丽水市缙云县",0),Position(4,"台州市临海市",1),
        Position(5,"台州市仙居县",0),Position(6,"台州市天台县",0),Position(7,"台州市三门县",0),
        Position(8,"舟山市嵊泗县",0),Position(9,"舟山市岱山县",0),Position(10,"舟山市定海区",1),
        Position(11,"金华市东阳市",1),Position(12,"金华市义乌市",1),Position(13,"金华市磐安县",0),
        Position(14,"金华市浦江县",0),Position(15,"绍兴市嵊州市",1),Position(16,"绍兴市诸暨市",1),
        Position(17,"绍兴市新昌县",0),Position(18,"绍兴市上虞区",1),Position(19,"绍兴市柯桥区",1),
        Position(20,"宁波市余姚市",1),Position(21,"宁波市宁海县",0),Position(22,"宁波市象山县",0),
        Position(23,"宁波市奉化区",1),Position(24,"宁波市鄞州区",1),Position(25,"宁波市北仑区",1),
        Position(26,"宁波市海曙区",1)]
edge=[
            [0,57,87,177,166,160,137,178,201,166,159,113,99,113,98,81,66,96,48,32,74,133,156,100,125,140,100],
            [57,0,34,132,154,133,128,181,252,211,198,75,54,101,43,82,21,97,81,48,109,144,176,120,152,174,126],
            [87,34,0,131,174,148,152,203,285,245,232,86,63,114,29,111,46,125,115,82,143,173,207,153,185,207,159],
            [177,132,131,0,104,64,106,152,321,270,245,64,79,55,93,123,113,122,165,148,179,153,188,160,188,215,172],
            [166,154,174,104,0,41,29,48,241,187,159,91,113,68,140,85,133,71,129,135,125,65,92,92,106,131,104],
            [160,133,148,64,41,0,47,89,270,216,189,62,85,34,111,86,112,77,133,128,138,94,128,111,134,159,123],
            [137,128,152,106,29,47,0,56,224,169,142,74,94,57,121,56,108,42,100,107,98,48,82,66,87,113,79],
            [178,181,203,152,48,89,56,0,208,154,124,131,150,111,176,101,162,85,133,150,120,49,55,83,82,100,91],
            [201,252,285,321,241,270,223,208,0,55,84,267,268,268,282,202,250,200,173,205,149,177,152,162,136,111,150],
            [166,211,245,270,187,216,170,154,55,0,30,217,222,215,238,152,205,149,130,163,103,123,98,111,83,56,98],
            [159,198,232,245,159,189,142,124,84,30,0,194,201,191,221,132,190,126,118,150,89,95,59,88,57,30,77],
            [113,75,86,64,92,62,74,131,267,217,194,0,23,27,49,65,54,69,102,85,120,112,149,107,139,166,119],
            [99,54,63,79,113,85,94,150,268,222,201,23,0,50,27,69,35,79,98,73,119,124,161,114,148,173,125],
            [113,101,114,55,58,34,57,111,267,215,191,27,50,0,76,69,80,68,114,103,125,101,138,105,135,162,118],
            [98,43,29,93,140,111,121,176,282,238,221,49,27,76,0,91,34,102,109,80,134,149,185,135,168,193,144],
            [81,82,111,123,85,86,56,101,202,152,132,65,69,69,91,0,66,16,46,50,56,62,96,44,78,104,55],
            [66,21,46,113,133,112,108,162,250,205,190,54,35,80,34,66,0,80,77,46,102,127,162,108,140,164,116],
            [96,97,125,122,71,77,42,85,200,149,126,69,79,68,102,16,80,0,58,66,60,48,84,39,71,98,51],
            [48,82,115,165,129,133,100,133,173,130,117,102,98,114,109,46,77,57,0,33,29,86,108,53,77,95,52],
            [32,48,82,148,135,128,107,150,205,163,150,85,73,103,80,50,46,66,33,0,61,107,135,79,107,128,81],
            [74,109,143,179,125,138,98,120,150,103,90,119,119,125,134,56,102,60,29,61,0,71,85,35,51,67,29],
            [133,144,173,153,65,95,48,49,177,123,95,112,124,101,149,62,127,48,86,107,71,0,36,36,41,65,43],
            [156,176,207,188,92,128,82,55,152,98,69,149,161,137,185,96,162,84,108,136,85,36,0,57,36,45,57],
            [100,120,153,160,92,111,66,83,162,111,88,107,114,105,135,44,108,39,53,79,35,36,57,0,33,59,13],
            [125,152,185,189,106,134,88,82,136,83,57,139,148,135,169,78,140,71,77,107,51,41,36,33,0,27,26],
            [140,174,207,215,131,159,113,100,111,56,30,166,173,162,193,104,164,98,95,128,67,65,45,59,27,0,49],
            [100,126,159,172,104,123,79,91,150,98,77,119,125,118,144,55,116,51,52,81,29,43,57,13,26,49,0]
        ]

# 直升机数据: v1用于启发式策略 (编号从0开始)
helicopters_v1=[Helicopter(0,'EC145',0,1,0,1793,3,2,680,246,0),
        Helicopter(1,'Bell412',0,1,1,1408,14,0,403,269,0),
        Helicopter(2,'Bell429',0,0,0,1371,8,0,761,285,0),
        Helicopter(3,'AW139',0,1,0,2778,13,2,568,290,0),
        Helicopter(4,'S-76',0,1,1,2129,10,2,639,269,0),
        Helicopter(5,'EC225',0,0,0,5744,20,4,857,260,0),
        Helicopter(6,'AW119Kx',0,1,0,1600,0,2,954,244,0),
        Helicopter(7,'AW109SP',0,1,1,1515,8,0,889,289,0),
        Helicopter(8,'H130',0,0,0,1050,8,0,617,240,0),
        Helicopter(9,'H135',0,0,1,1455,5,2,635,254,0),
        Helicopter(10,'H145',0,0,1,1793,8,1,680,246,0),
        Helicopter(11,'AS350B3',0,0,1,1018,6,0,652,235,0),
        Helicopter(12,'超黄蜂',0,1,1,3000,10,5,700,220,0)]

# 直升机数据: 用于RL模型 (编号从1开始)
helicopters=[Helicopter(1,'EC145',0,1,0,1793,3,2,680,246,0),
        Helicopter(2,'Bell412',0,1,1,1408,14,0,403,269,0),
        Helicopter(3,'Bell429',0,0,0,1371,8,0,761,285,0),
        Helicopter(4,'AW139',0,1,0,2778,13,2,568,290,0),
        Helicopter(5,'S-76',0,1,1,2129,10,2,639,269,0),
        Helicopter(6,'EC225',0,0,0,5744,20,4,857,260,0),
        Helicopter(7,'AW119Kx',0,1,0,1600,0,2,954,244,0),
        Helicopter(8,'AW109SP',0,1,1,1515,8,0,889,289,0),
        Helicopter(9,'H130',0,0,0,1050,8,0,617,240,0),
        Helicopter(10,'H135',0,0,1,1455,5,2,635,254,0),
        Helicopter(11,'H145',0,0,1,1793,8,1,680,246,0),
        Helicopter(12,'AS350B3',0,0,1,1018,6,0,652,235,0),
        Helicopter(13,'超黄蜂',0,1,1,3000,10,5,700,220,0)]

# ==============================================================================
# 任务生成函数 (从 1_aerta_test_rlhf_v2.py 复制)
# ==============================================================================

def generate_tasks(seed, total_tasks, max_tasks_per_time, periods, start_clock=1, start_number=1):
    random.seed(seed)
    new_tasks = []
    tasks_count_per_time = {}  # Track the number of tasks at each publish_clock

    number=start_number
    while len(new_tasks) < total_tasks:
        publish_clock = random.randint(1, periods) + start_clock - 1
        pos=random.randint(1,26)
        search=0
        material=0
        person=0
        medical=0
        type=random.randint(1,4)
        if type==1:
            search=1
        elif type==2:
            material=random.randint(1,600)
        elif type==3:
            person=random.randint(1,10)
        elif type==4:
            medical=random.randint(1,5)
        if (search+material+person+medical)==0:
            search=1
        a=random.randint(10,100)
        b=a+random.randint(0,30)
        c=b+random.randint(0,30)
        d=random.random()
        m=[a,b,c,d]
        task=Task(number,publish_clock,0,0,pos,search,material,person,medical,m)
        
        while tasks_count_per_time.get(publish_clock, 0) >= max_tasks_per_time:
            publish_clock = random.randint(1, periods) + start_clock - 1

        new_tasks.append(task)
        tasks_count_per_time[publish_clock] = tasks_count_per_time.get(publish_clock, 0) + 1
        number+=1

    new_tasks.sort(key=lambda task: task.publish_clock)
    return new_tasks

def generate_base_data(seed=86,total_tasks_per_set=20,max_tasks_per_time=3,periods_per_set=10,num_datasets=10):
    """调用generate_tasks多次，生成一个大规模、分阶段的任务集。"""
    task_sets = []
    last_clock = 0  # Initial start clock
    last_number = 1
    for i in range(num_datasets):
        # 每次调用都使用不同的、但与主seed相关的seed
        new_tasks = generate_tasks(seed + i, total_tasks_per_set, max_tasks_per_time, periods_per_set, start_clock=last_clock ,start_number = last_number)
        if not new_tasks:
            continue
        task_sets.extend(new_tasks)
        # 更新下一次任务生成的起始时间和编号，模拟任务的连续到达
        last_clock = new_tasks[-1].publish_clock + 170  # Update start_clock based on the last publish_clock plus a buffer
        last_number = last_number + total_tasks_per_set
    return task_sets

# ==============================================================================
# 策略评估函数
# ==============================================================================

def evaluate_nearby(tasks):
    """使用临近策略运行一次完整的评估，并返回最终总效益。"""
    # 创建全局数据的深拷贝，以确保评估的独立性
    positions_copy = copy.deepcopy(positions)
    helicopters_copy = copy.deepcopy(helicopters_v1)

    # 注意：启发式策略使用 helicopters_v1 (编号从0开始)
    positions_copy[0].helicopters = [h.number for h in helicopters_copy]
    env = aerta_helicopter_nearby_free.env(positions=positions_copy, helicopters=helicopters_copy, tasks=tasks, edge=edge)
    env.reset()
    value = []
    
    # 记录原始任务总数
    original_task_count = len(tasks)
    
    for agent in env.agent_iter():
        obs, rew, done, info = env.last()
        if done:
            break
        # 在这些简单策略的环境中，动作总是1（参与）
        act = 1 if not done else None
        env.step(act)
        v, t = env.render()
        if v is not None:
            value.append(v)
    
    # 获取失败任务数量
    failed_tasks_count = 0
    if hasattr(env.unwrapped, 'delete_task'):
        failed_tasks_count = len(env.unwrapped.delete_task)
    
    env.close()
    if not value:
        return 0, failed_tasks_count
    return value[-1], failed_tasks_count

def evaluate_bestfit(tasks):
    """使用最优匹配策略运行一次完整的评估，并返回最终总效益。"""
    # 创建全局数据的深拷贝，以确保评估的独立性
    positions_copy = copy.deepcopy(positions)
    helicopters_copy = copy.deepcopy(helicopters_v1)

    # 注意：启发式策略使用 helicopters_v1 (编号从0开始)
    positions_copy[0].helicopters = [h.number for h in helicopters_copy]
    env = aerta_helicopter_bestfit_free.env(positions=positions_copy, helicopters=helicopters_copy, tasks=tasks, edge=edge)
    env.reset()
    value = []
    
    # 记录原始任务总数
    original_task_count = len(tasks)
    
    for agent in env.agent_iter():
        obs, rew, done, info = env.last()
        if done:
            break
        # 在这些简单策略的环境中，动作总是1（参与）
        act = 1 if not done else None
        env.step(act)
        v, t = env.render()
        if v is not None:
            value.append(v)
    
    # 获取失败任务数量
    failed_tasks_count = 0
    if hasattr(env.unwrapped, 'delete_task'):
        failed_tasks_count = len(env.unwrapped.delete_task)
    
    env.close()
    if not value:
        return 0, failed_tasks_count
    return value[-1], failed_tasks_count

def rl_policy(obs, model):
    """使用加载好的模型进行预测。"""
    return model.predict(obs, deterministic=True)[0]

def evaluate_rl(tasks, model_path, model_name):
    """
    评估一个指定的RL模型。
    此函数是通用的，通过模型路径加载并运行模拟。
    """
    print(f"Running {model_name} Strategy...")
    # 创建全局数据的深拷贝，以确保评估的独立性
    import copy
    positions_copy = copy.deepcopy(positions)
    helicopters_copy = copy.deepcopy(helicopters)

    # 注意：RL策略使用 helicopters (编号从1开始)
    positions_copy[0].helicopters=[h.number for h in helicopters_copy]
    env = aerta_position_V12.env(positions=positions_copy,helicopters=helicopters_copy,tasks=tasks,edge=edge)
    env.reset()
    
    # 记录原始任务总数
    original_task_count = len(tasks)
    
    # 在模拟开始前只加载一次模型
    try:
        model = PPO.load(model_path, device='cpu')
        print(f"  - Model '{model_path}' loaded. Starting simulation...")
    except Exception as e:
        print(f"  - Error loading RL model from '{model_path}': {e}")
        return 0, 0

    value=[]
    step_count = 0
    for agent in env.agent_iter():
        obs, rew, terminations, info = env.last()
        if terminations:
            env.step(None)
            continue
        
        act = rl_policy(obs, model)
        env.step(act)
        
        v,t = env.render()
        if v is not None:
            value.append(v)
        
        step_count += 1
        # if step_count > 0 and step_count % 1000 == 0:
        #     print(f"  ... simulation step {step_count}")

    # 获取失败任务数量
    failed_tasks_count = 0
    if hasattr(env.unwrapped, 'delete_task'):
        failed_tasks_count = len(env.unwrapped.delete_task)
    
    env.close()
    print(f"  - {model_name} simulation finished after {step_count} steps.")
    if not value:
        return 0, failed_tasks_count
    return value[-1], failed_tasks_count

# ==============================================================================
# 日志记录功能
# ==============================================================================

class Logger:
    def __init__(self, filename):
        self.terminal = sys.stdout
        self.log = open(filename, 'w', encoding='utf-8')
        self.last_message = ""
        self.repeat_count = 0
        self.max_repeats = 5  # 允许的最大重复次数
        
    def write(self, message):
        # 如果是空消息或只有空白字符，直接写入
        if not message or message.isspace():
            self.terminal.write(message)
            self.log.write(message)
            return
            
        # 检查是否是重复消息
        if message == self.last_message:
            self.repeat_count += 1
            if self.repeat_count <= self.max_repeats:
                self.terminal.write(message)
                self.log.write(message)
            elif self.repeat_count == self.max_repeats + 1:
                # 输出一条提示消息，表明后续重复消息将被过滤
                note = "\n[注意: 检测到重复输出，后续相同内容将被过滤]\n"
                self.terminal.write(note)
                self.log.write(note)
        else:
            # 如果有被过滤的消息，输出一条汇总信息
            if self.repeat_count > self.max_repeats:
                summary = f"\n[已过滤 {self.repeat_count - self.max_repeats} 条重复消息]\n"
                self.terminal.write(summary)
                self.log.write(summary)
            
            # 重置计数器并写入新消息
            self.repeat_count = 1
            self.last_message = message
            self.terminal.write(message)
            self.log.write(message)
        
    def flush(self):
        self.terminal.flush()
        self.log.flush()

# ==============================================================================
# 主执行块
# ==============================================================================

if __name__ == '__main__':
    # 设置日志文件
    current_time = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    log_file = f"evaluation_results_{current_time}.txt"
    
    # 重定向标准输出到日志文件
    sys.stdout = Logger(log_file)
    
    print(f"评估开始时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    num_eval_runs = 10
    base_seed = 76

    # 定义所有要评估的RL模型
    rl_models_to_evaluate = {
        "Base RL": "nohf_7685_15e6",
        "LLM-Tuned RL": "rlhf_7685_15e6_7685_1e6_v3_394",
        "Reward-Tuned RL": "rlhf_7685_15e6_7685_5e6_v3_400"
    }

    # 初始化结果存储
    nearby_results = []
    bestfit_results = []
    # 使用一个字典来存储所有RL模型的结果
    rl_results = {name: [] for name in rl_models_to_evaluate.keys()}
    
    # 存储每轮的最佳效益
    best_benefits_per_round = []
    
    # 存储每轮各策略与最佳效益的差距
    nearby_gaps = []
    bestfit_gaps = []
    rl_gaps = {name: [] for name in rl_models_to_evaluate.keys()}
    
    # 存储每种策略的失败任务数量
    nearby_failed_tasks = []
    bestfit_failed_tasks = []
    rl_failed_tasks = {name: [] for name in rl_models_to_evaluate.keys()}
    
    # 存储每轮任务总数
    total_tasks_per_round = []

    print(f"开始对 {2 + len(rl_models_to_evaluate)} 种策略在 {num_eval_runs} 个不同任务集上进行性能测试...")
    
    for i in range(num_eval_runs):
        current_seed = base_seed + i*10

        print(f"\n--- 运行评估集 {i+1}/{num_eval_runs} (种子: {current_seed}) ---")
        
        tasks = generate_base_data(seed=current_seed)
        total_tasks_count = len(tasks)
        total_tasks_per_round.append(total_tasks_count)
        print(f"  - 本轮任务总数: {total_tasks_count}")
        
        # 用于存储当前轮次的所有效益值
        current_round_benefits = {}
        
        # 评估临近策略
        print("Running Nearby Strategy...")
        tasks_for_ny = copy.deepcopy(tasks)
        benefit_ny, failed_ny = evaluate_nearby(tasks_for_ny)
        nearby_results.append(benefit_ny)
        nearby_failed_tasks.append(failed_ny)
        current_round_benefits["临近策略"] = benefit_ny
        print(f"  - 临近策略 (Nearby) 获得效益: {benefit_ny:.2f}")
        print(f"  - 临近策略 (Nearby) 失败任务数: {failed_ny} ({failed_ny/total_tasks_count*100:.2f}%)")

        # 评估最优匹配策略
        print("Running Best-fit Strategy...")
        tasks_for_bf = copy.deepcopy(tasks)
        benefit_bf, failed_bf = evaluate_bestfit(tasks_for_bf)
        bestfit_results.append(benefit_bf)
        bestfit_failed_tasks.append(failed_bf)
        current_round_benefits["最优匹配策略"] = benefit_bf
        print(f"  - 最优匹配策略 (Best-fit) 获得效益: {benefit_bf:.2f}")
        print(f"  - 最优匹配策略 (Best-fit) 失败任务数: {failed_bf} ({failed_bf/total_tasks_count*100:.2f}%)")

        # 评估所有强化学习策略
        for model_name, model_path in rl_models_to_evaluate.items():
            tasks_for_rl = copy.deepcopy(tasks)
            benefit_rl, failed_rl = evaluate_rl(tasks_for_rl, model_path, model_name)
            rl_results[model_name].append(benefit_rl)
            rl_failed_tasks[model_name].append(failed_rl)
            current_round_benefits[model_name] = benefit_rl
            print(f"  - {model_name} 策略获得效益: {benefit_rl:.2f}")
            print(f"  - {model_name} 策略失败任务数: {failed_rl} ({failed_rl/total_tasks_count*100:.2f}%)")
        
        # 计算当前轮次的最佳效益
        best_benefit = max(current_round_benefits.values())
        best_benefits_per_round.append(best_benefit)
        
        # 计算每个策略与最佳效益的差距
        nearby_gaps.append(best_benefit - current_round_benefits["临近策略"])
        bestfit_gaps.append(best_benefit - current_round_benefits["最优匹配策略"])
        for model_name in rl_models_to_evaluate.keys():
            rl_gaps[model_name].append(best_benefit - current_round_benefits[model_name])

    # --- 计算并打印最终的统计结果 ---
    print("\n" + "="*50)
    print(" " * 15 + "评 估 结 果 总 结")
    print("="*50)

    # 计算平均任务数
    avg_total_tasks = np.mean(total_tasks_per_round)
    print(f"\n平均每轮任务总数: {avg_total_tasks:.1f}")

    # 打印启发式策略结果
    nearby_mean = np.mean(nearby_results)
    nearby_gap_mean = np.mean(nearby_gaps)
    nearby_gap_std = np.std(nearby_gaps)
    nearby_failed_mean = np.mean(nearby_failed_tasks)
    nearby_failed_ratio = nearby_failed_mean / avg_total_tasks * 100
    print("\n临近策略 (Nearby Strategy):")
    print(f"  - 各轮效益: {[round(r, 2) for r in nearby_results]}")
    print(f"  - 平均效益: {nearby_mean:.2f}")
    print(f"  - 各轮与最佳效益差距: {[round(gap, 2) for gap in nearby_gaps]}")
    print(f"  - 与最佳效益平均差距: {nearby_gap_mean:.2f}")
    print(f"  - 差距标准差: {nearby_gap_std:.2f}")
    print(f"  - 各轮失败任务数: {nearby_failed_tasks}")
    print(f"  - 平均失败任务数: {nearby_failed_mean:.2f}")
    print(f"  - 平均失败任务占比: {nearby_failed_ratio:.2f}%")

    bestfit_mean = np.mean(bestfit_results)
    bestfit_gap_mean = np.mean(bestfit_gaps)
    bestfit_gap_std = np.std(bestfit_gaps)
    bestfit_failed_mean = np.mean(bestfit_failed_tasks)
    bestfit_failed_ratio = bestfit_failed_mean / avg_total_tasks * 100
    print("\n最优匹配策略 (Best-fit Strategy):")
    print(f"  - 各轮效益: {[round(r, 2) for r in bestfit_results]}")
    print(f"  - 平均效益: {bestfit_mean:.2f}")
    print(f"  - 各轮与最佳效益差距: {[round(gap, 2) for gap in bestfit_gaps]}")
    print(f"  - 与最佳效益平均差距: {bestfit_gap_mean:.2f}")
    print(f"  - 差距标准差: {bestfit_gap_std:.2f}")
    print(f"  - 各轮失败任务数: {bestfit_failed_tasks}")
    print(f"  - 平均失败任务数: {bestfit_failed_mean:.2f}")
    print(f"  - 平均失败任务占比: {bestfit_failed_ratio:.2f}%")
    
    # 打印所有RL模型的结果
    for model_name, results in rl_results.items():
        mean = np.mean(results)
        gap_mean = np.mean(rl_gaps[model_name])
        gap_std = np.std(rl_gaps[model_name])
        failed_mean = np.mean(rl_failed_tasks[model_name])
        failed_ratio = failed_mean / avg_total_tasks * 100
        print(f"\n{model_name} 策略:")
        print(f"  - 各轮效益: {[round(r, 2) for r in results]}")
        print(f"  - 平均效益: {mean:.2f}")
        print(f"  - 各轮与最佳效益差距: {[round(gap, 2) for gap in rl_gaps[model_name]]}")
        print(f"  - 与最佳效益平均差距: {gap_mean:.2f}")
        print(f"  - 差距标准差: {gap_std:.2f}")
        print(f"  - 各轮失败任务数: {rl_failed_tasks[model_name]}")
        print(f"  - 平均失败任务数: {failed_mean:.2f}")
        print(f"  - 平均失败任务占比: {failed_ratio:.2f}%")

    # 打印每轮的最佳效益和最佳策略
    print("\n每轮最佳效益:")
    for i, best in enumerate(best_benefits_per_round):
        # 找出达到最佳效益的策略
        best_strategies = []
        if nearby_results[i] == best:
            best_strategies.append("临近策略")
        if bestfit_results[i] == best:
            best_strategies.append("最优匹配策略")
        for model_name in rl_models_to_evaluate.keys():
            if rl_results[model_name][i] == best:
                best_strategies.append(model_name)
        
        print(f"  - 轮次 {i+1}: {best:.2f} (最佳策略: {', '.join(best_strategies)})")

    print("\n" + "="*50)
    print(f"评估结束时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"完整日志已保存至: {log_file}")
    
    # 恢复标准输出
    sys.stdout = sys.stdout.terminal 