import numpy as np
import random

# 从pettingzoo环境导入必要的类和函数
from pettingzoo.userhfmappo import aerta_helicopter_bestfit_free
from pettingzoo.userhfmappo import aerta_helicopter_nearby_free
from pettingzoo.userhfmappo.aerta_position_V12 import Position, Helicopter, Task

# ==============================================================================
# 核心数据定义 (从 1_aerta_test_rlhf_v2.py 复制)
# ==============================================================================

positions=[Position(0,"萧山机场",1),Position(1,"杭州市富阳区",1),
        Position(2,"杭州市桐庐县",0),Position(3,"丽水市缙云县",0),Position(4,"台州市临海市",1),
        Position(5,"台州市仙居县",0),Position(6,"台州市天台县",0),Position(7,"台州市三门县",0),
        Position(8,"舟山市嵊泗县",0),Position(9,"舟山市岱山县",0),Position(10,"舟山市定海区",1),
        Position(11,"金华市东阳市",1),Position(12,"金华市义乌市",1),Position(13,"金华市磐安县",0),
        Position(14,"金华市浦江县",0),Position(15,"绍兴市嵊州市",1),Position(16,"绍兴市诸暨市",1),
        Position(17,"绍兴市新昌县",0),Position(18,"绍兴市上虞区",1),Position(19,"绍兴市柯桥区",1),
        Position(20,"宁波市余姚市",1),Position(21,"宁波市宁海县",0),Position(22,"宁波市象山县",0),
        Position(23,"宁波市奉化区",1),Position(24,"宁波市鄞州区",1),Position(25,"宁波市北仑区",1),
        Position(26,"宁波市海曙区",1)]
edge=[
            [0,57,87,177,166,160,137,178,201,166,159,113,99,113,98,81,66,96,48,32,74,133,156,100,125,140,100],
            [57,0,34,132,154,133,128,181,252,211,198,75,54,101,43,82,21,97,81,48,109,144,176,120,152,174,126],
            [87,34,0,131,174,148,152,203,285,245,232,86,63,114,29,111,46,125,115,82,143,173,207,153,185,207,159],
            [177,132,131,0,104,64,106,152,321,270,245,64,79,55,93,123,113,122,165,148,179,153,188,160,188,215,172],
            [166,154,174,104,0,41,29,48,241,187,159,91,113,68,140,85,133,71,129,135,125,65,92,92,106,131,104],
            [160,133,148,64,41,0,47,89,270,216,189,62,85,34,111,86,112,77,133,128,138,94,128,111,134,159,123],
            [137,128,152,106,29,47,0,56,224,169,142,74,94,57,121,56,108,42,100,107,98,48,82,66,87,113,79],
            [178,181,203,152,48,89,56,0,208,154,124,131,150,111,176,101,162,85,133,150,120,49,55,83,82,100,91],
            [201,252,285,321,241,270,223,208,0,55,84,267,268,268,282,202,250,200,173,205,149,177,152,162,136,111,150],
            [166,211,245,270,187,216,170,154,55,0,30,217,222,215,238,152,205,149,130,163,103,123,98,111,83,56,98],
            [159,198,232,245,159,189,142,124,84,30,0,194,201,191,221,132,190,126,118,150,89,95,59,88,57,30,77],
            [113,75,86,64,92,62,74,131,267,217,194,0,23,27,49,65,54,69,102,85,120,112,149,107,139,166,119],
            [99,54,63,79,113,85,94,150,268,222,201,23,0,50,27,69,35,79,98,73,119,124,161,114,148,173,125],
            [113,101,114,55,58,34,57,111,267,215,191,27,50,0,76,69,80,68,114,103,125,101,138,105,135,162,118],
            [98,43,29,93,140,111,121,176,282,238,221,49,27,76,0,91,34,102,109,80,134,149,185,135,168,193,144],
            [81,82,111,123,85,86,56,101,202,152,132,65,69,69,91,0,66,16,46,50,56,62,96,44,78,104,55],
            [66,21,46,113,133,112,108,162,250,205,190,54,35,80,34,66,0,80,77,46,102,127,162,108,140,164,116],
            [96,97,125,122,71,77,42,85,200,149,126,69,79,68,102,16,80,0,58,66,60,48,84,39,71,98,51],
            [48,82,115,165,129,133,100,133,173,130,117,102,98,114,109,46,77,57,0,33,29,86,108,53,77,95,52],
            [32,48,82,148,135,128,107,150,205,163,150,85,73,103,80,50,46,66,33,0,61,107,135,79,107,128,81],
            [74,109,143,179,125,138,98,120,150,103,90,119,119,125,134,56,102,60,29,61,0,71,85,35,51,67,29],
            [133,144,173,153,65,95,48,49,177,123,95,112,124,101,149,62,127,48,86,107,71,0,36,36,41,65,43],
            [156,176,207,188,92,128,82,55,152,98,69,149,161,137,185,96,162,84,108,136,85,36,0,57,36,45,57],
            [100,120,153,160,92,111,66,83,162,111,88,107,114,105,135,44,108,39,53,79,35,36,57,0,33,59,13],
            [125,152,185,189,106,134,88,82,136,83,57,139,148,135,169,78,140,71,77,107,51,41,36,33,0,27,26],
            [140,174,207,215,131,159,113,100,111,56,30,166,173,162,193,104,164,98,95,128,67,65,45,59,27,0,49],
            [100,126,159,172,104,123,79,91,150,98,77,119,125,118,144,55,116,51,52,81,29,43,57,13,26,49,0]
        ]

helicopters_v1=[Helicopter(0,'EC145',0,1,0,1793,3,2,680,246,0),
        Helicopter(1,'Bell412',0,1,1,1408,14,0,403,269,0),
        Helicopter(2,'Bell429',0,0,0,1371,8,0,761,285,0),
        Helicopter(3,'AW139',0,1,0,2778,13,2,568,290,0),
        Helicopter(4,'S-76',0,1,1,2129,10,2,639,269,0),
        Helicopter(5,'EC225',0,0,0,5744,20,4,857,260,0),
        Helicopter(6,'AW119Kx',0,1,0,1600,0,2,954,244,0),
        Helicopter(7,'AW109SP',0,1,1,1515,8,0,889,289,0),
        Helicopter(8,'H130',0,0,0,1050,8,0,617,240,0),
        Helicopter(9,'H135',0,0,1,1455,5,2,635,254,0),
        Helicopter(10,'H145',0,0,1,1793,8,1,680,246,0),
        Helicopter(11,'AS350B3',0,0,1,1018,6,0,652,235,0),
        Helicopter(12,'超黄蜂',0,1,1,3000,10,5,700,220,0)]

# ==============================================================================
# 任务生成函数 (从 1_aerta_test_rlhf_v2.py 复制)
# ==============================================================================

def generate_tasks(seed, total_tasks, max_tasks_per_time, periods, start_clock=1, start_number=1):
    random.seed(seed)
    new_tasks = []
    tasks_count_per_time = {}  # Track the number of tasks at each publish_clock

    number=start_number
    while len(new_tasks) < total_tasks:
        publish_clock = random.randint(1, periods) + start_clock - 1
        pos=random.randint(1,26)
        search=0
        material=0
        person=0
        medical=0
        type=random.randint(1,4)
        if type==1:
            search=1
        elif type==2:
            material=random.randint(1,600)
        elif type==3:
            person=random.randint(1,10)
        elif type==4:
            medical=random.randint(1,5)
        if (search+material+person+medical)==0:
            search=1
        a=random.randint(10,100)
        b=a+random.randint(0,30)
        c=b+random.randint(0,30)
        d=random.random()
        m=[a,b,c,d]
        task=Task(number,publish_clock,0,0,pos,search,material,person,medical,m)
        
        while tasks_count_per_time.get(publish_clock, 0) >= max_tasks_per_time:
            publish_clock = random.randint(1, periods) + start_clock - 1

        new_tasks.append(task)
        tasks_count_per_time[publish_clock] = tasks_count_per_time.get(publish_clock, 0) + 1
        number+=1

    new_tasks.sort(key=lambda task: task.publish_clock)
    return new_tasks

def generate_base_data(seed=86,total_tasks_per_set=20,max_tasks_per_time=3,periods_per_set=10,num_datasets=10):
    """调用generate_tasks多次，生成一个大规模、分阶段的任务集。"""
    task_sets = []
    last_clock = 0  # Initial start clock
    last_number = 1
    for i in range(num_datasets):
        # 每次调用都使用不同的、但与主seed相关的seed
        new_tasks = generate_tasks(seed + i, total_tasks_per_set, max_tasks_per_time, periods_per_set, start_clock=last_clock ,start_number = last_number)
        if not new_tasks:
            continue
        task_sets.extend(new_tasks)
        # 更新下一次任务生成的起始时间和编号，模拟任务的连续到达
        last_clock = new_tasks[-1].publish_clock + 170  # Update start_clock based on the last publish_clock plus a buffer
        last_number = last_number + total_tasks_per_set
    return task_sets

# ==============================================================================
# 策略评估函数
# ==============================================================================

def run_nearby_eval(tasks, positions_data, helicopters_data, edge_data):
    """使用临近策略运行一次完整的评估，并返回最终总效益。"""
    # 每次评估前，需要深拷贝数据，以防被环境内部修改影响下一次评估
    import copy
    positions_copy = copy.deepcopy(positions_data)
    helicopters_copy = copy.deepcopy(helicopters_data)
    
    positions_copy[0].helicopters = [h.number for h in helicopters_copy]
    env = aerta_helicopter_nearby_free.env(positions=positions_copy, helicopters=helicopters_copy, tasks=tasks, edge=edge_data)
    env.reset()
    benefits_over_time = []
    for agent in env.agent_iter():
        obs, rew, done, info = env.last()
        if done:
            break
        # 临近策略环境的决策逻辑已内置，只需执行一个有效动作（如1）即可触发
        act = 1 if not done else None
        env.step(act)
        v, t = env.render()
        benefits_over_time.append(v)
    env.close()
    # 返回倒数第二个记录的效益值，并包装成元组以避免解包错误
    final_benefit = benefits_over_time[-2] if len(benefits_over_time) > 1 else 0
    return final_benefit, 0

def run_bestfit_eval(tasks, positions_data, helicopters_data, edge_data):
    """使用最优匹配策略运行一次完整的评估，并返回最终总效益。"""
    import copy
    positions_copy = copy.deepcopy(positions_data)
    helicopters_copy = copy.deepcopy(helicopters_data)
    
    positions_copy[0].helicopters = [h.number for h in helicopters_copy]
    env = aerta_helicopter_bestfit_free.env(positions=positions_copy, helicopters=helicopters_copy, tasks=tasks, edge=edge_data)
    env.reset()
    benefits_over_time = []
    for agent in env.agent_iter():
        obs, rew, done, info = env.last()
        if done:
            break
        # 最优匹配策略环境的决策逻辑已内置，只需执行一个有效动作（如1）即可触发
        act = 1 if not done else None
        env.step(act)
        v, t = env.render()
        benefits_over_time.append(v)
    env.close()
    # 返回倒数第二个记录的效益值，并包装成元组以避免解包错误
    final_benefit = benefits_over_time[-2] if len(benefits_over_time) > 1 else 0
    return final_benefit, 0

# ==============================================================================
# 主执行块
# ==============================================================================

if __name__ == '__main__':
    num_eval_runs = 10
    base_seed = 76  # 使用一个固定的基础种子以保证结果可复现

    nearby_results = []
    bestfit_results = []

    print(f"开始对两种经验策略在 {num_eval_runs} 个不同任务集上进行性能测试...")

    for i in range(num_eval_runs):
        current_seed = base_seed + i
        print(f"\n--- 运行评估集 {i+1}/{num_eval_runs} (种子: {current_seed}) ---")
        
        # 为本次运行生成一组与原始脚本规模相同的任务集 (200个任务)
        tasks = generate_base_data(seed=current_seed)
        
        # 深拷贝以确保原始数据不受污染
        import copy
        tasks_for_ny = copy.deepcopy(tasks)
        tasks_for_bf = copy.deepcopy(tasks)

        # 评估临近策略
        benefit_ny, _ = run_nearby_eval(tasks_for_ny, positions, helicopters_v1, edge)
        nearby_results.append(benefit_ny)
        print(f"临近策略 (Nearby) 获得效益: {benefit_ny:.2f}")

        # 评估最优匹配策略
        benefit_bf, _ = run_bestfit_eval(tasks_for_bf, positions, helicopters_v1, edge)
        bestfit_results.append(benefit_bf)
        print(f"最优匹配策略 (Best-fit) 获得效益: {benefit_bf:.2f}")

    # 计算并打印最终的统计结果
    nearby_mean = np.mean(nearby_results)
    nearby_std = np.std(nearby_results)
    bestfit_mean = np.mean(bestfit_results)
    bestfit_std = np.std(bestfit_results)

    print("\n" + "="*30)
    print("      评 估 结 果 总 结")
    print("="*30)

    print("\n临近策略 (Nearby Strategy):")
    print(f"  - 各轮效益: {[round(r, 2) for r in nearby_results]}")
    print(f"  - 平均效益: {nearby_mean:.2f}")
    print(f"  - 效益标准差: {nearby_std:.2f}")

    print("\n最优匹配策略 (Best-fit Strategy):")
    print(f"  - 各轮效益: {[round(r, 2) for r in bestfit_results]}")
    print(f"  - 平均效益: {bestfit_mean:.2f}")
    print(f"  - 效益标准差: {bestfit_std:.2f}")
    print("\n" + "="*30) 