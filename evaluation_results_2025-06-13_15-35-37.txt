评估开始时间: 2025-06-13 15:35:37
开始对 5 种策略在 10 个不同任务集上进行性能测试...

--- 运行评估集 1/10 (种子: 76) ---
Running Nearby Strategy...
[15, 19, 10, 17, 62, 90, 110, 112, 107, 128, 131, 148, 144, 151, 157, 171, 199, 183]
18
  - 注意：未能从环境中获取时间满意度数据
  - 临近策略 (Nearby) 获得效益: 368.64
Running Best-fit Strategy...
[19, 10, 17, 62, 90, 110, 112, 108, 102, 152, 158, 167, 198]
13
  - 注意：未能从环境中获取时间满意度数据
  - 最优匹配策略 (Best-fit) 获得效益: 375.04
Running Base RL Strategy...
  - Model 'nohf_7685_15e6' loaded. Starting simulation...
  ... simulation step 1000
  ... simulation step 2000
  ... simulation step 3000
  ... simulation step 4000
  ... simulation step 5000
  ... simulation step 6000
  ... simulation step 7000
  ... simulation step 8000
  ... simulation step 9000
  ... simulation step 10000
  ... simulation step 11000
  ... simulation step 12000
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
Clock:1697-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-14', 'H145[1,1793,8,1]/-36', 'AS350B3[1,1018,6,0]/-38', 'EC225[0,5744,20,4]/-40', 'Bell412[1,1408,14,0]/-43', 'H135[1,1455,5,2]/-54']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128.0, -188.0, -200]; Helicopters=['AW109SP[1,931,8,0]/-47']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['AW119Kx[0,1600,0,0]/-4', 'AW139[0,2647,13,1]/-16']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -66, -67, -107, -144, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123.0, -142, -162, -199]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152.0, -177, -179, -197, -183, -187]; Helicopters=['超黄蜂[1,3000,10,0]/-22', 'S-76[1,2129,2,2]/-3']
position_9舟山市岱山县: Tasks=[-100, -85, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=['EC145[0,1793,3,2]/0']
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -169, -178, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122.0, -132, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H130[0,1050,8,0]/8']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 77, 90, 110, 109, 112, 102, 116, 123, 122, 136, 128, 141, 152, 188], length is 18
total value:391.3215632968463
the average time satisfaction is 0.572272602331423
the variance is 0.19986550243701687
  - 注意：未能从环境中获取时间满意度数据
  - Base RL simulation finished after 12204 steps.
  - Base RL 策略获得效益: 391.32
Running LLM-Tuned RL Strategy...
  - Model 'rlhf_7685_15e6_7685_1e6_v3_394' loaded. Starting simulation...
  ... simulation step 1000
  ... simulation step 2000
  ... simulation step 3000
  ... simulation step 4000
  ... simulation step 5000
  ... simulation step 6000
  ... simulation step 7000
  ... simulation step 8000
  ... simulation step 9000
  ... simulation step 10000
  ... simulation step 11000
  ... simulation step 12000
Clock:1685-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/145', 'AS350B3[1,1018,6,0]/-44', 'EC225[0,5744,20,4]/-49', 'H130[0,1050,8,0]/-49']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31.0, -48, -55, -77, -71, -97, -127, -129, -166, -186]; Helicopters=['S-76[1,1545,10,1]/-30']
position_2杭州市桐庐县: Tasks=[-13, -33.0, -68, -128, -188, -200]; Helicopters=['Bell412[1,1183,12,0]/-7']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62.0, -86, -102.0, -165, -198]; Helicopters=['EC145[0,1793,3,0]/-36', 'AW119Kx[0,1469,0,1]/-21']
position_6台州市天台县: Tasks=[-10.0, -37, -58, -44, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-9']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47.0, -84, -119, -103, -108, -152, -177, -179, -197, -183.0, -187]; Helicopters=['AW139[0,2778,5,0]/-27', '超黄蜂[1,3000,10,5]/-81']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149.0, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -1, -8.0, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -174, -169, -178, -164, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -132, -122.0, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['H135[1,1021,5,2]/10']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65.0, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,8,1]/1']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 8, 33, 31, 51, 47, 65, 62, 90, 110, 109, 112, 116, 102, 122, 136, 141, 144, 149, 183], length is 23
total value:394.70874210714305
the average time satisfaction is 0.5588753431622718
the variance is 0.20314466708547593
Clock:1685-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/145', 'AS350B3[1,1018,6,0]/-44', 'EC225[0,5744,20,4]/-49', 'H130[0,1050,8,0]/-49']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31.0, -48, -55, -77, -71, -97, -127, -129, -166, -186]; Helicopters=['S-76[1,1545,10,1]/-30']
position_2杭州市桐庐县: Tasks=[-13, -33.0, -68, -128, -188, -200]; Helicopters=['Bell412[1,1183,12,0]/-7']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62.0, -86, -102.0, -165, -198]; Helicopters=['EC145[0,1793,3,0]/-36', 'AW119Kx[0,1469,0,1]/-21']
position_6台州市天台县: Tasks=[-10.0, -37, -58, -44, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-9']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47.0, -84, -119, -103, -108, -152, -177, -179, -197, -183.0, -187]; Helicopters=['AW139[0,2778,5,0]/-27', '超黄蜂[1,3000,10,5]/-81']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149.0, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -1, -8.0, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -174, -169, -178, -164, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -132, -122.0, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['H135[1,1021,5,2]/10']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65.0, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,8,1]/1']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 8, 33, 31, 51, 47, 65, 62, 90, 110, 109, 112, 116, 102, 122, 136, 141, 144, 149, 183], length is 23
total value:394.70874210714305
the average time satisfaction is 0.5588753431622718
the variance is 0.20314466708547593
Clock:1685-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/145', 'AS350B3[1,1018,6,0]/-44', 'EC225[0,5744,20,4]/-49', 'H130[0,1050,8,0]/-49']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31.0, -48, -55, -77, -71, -97, -127, -129, -166, -186]; Helicopters=['S-76[1,1545,10,1]/-30']
position_2杭州市桐庐县: Tasks=[-13, -33.0, -68, -128, -188, -200]; Helicopters=['Bell412[1,1183,12,0]/-7']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62.0, -86, -102.0, -165, -198]; Helicopters=['EC145[0,1793,3,0]/-36', 'AW119Kx[0,1469,0,1]/-21']
position_6台州市天台县: Tasks=[-10.0, -37, -58, -44, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-9']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47.0, -84, -119, -103, -108, -152, -177, -179, -197, -183.0, -187]; Helicopters=['AW139[0,2778,5,0]/-27', '超黄蜂[1,3000,10,5]/-81']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149.0, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -1, -8.0, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -174, -169, -178, -164, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -132, -122.0, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['H135[1,1021,5,2]/10']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65.0, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,8,1]/1']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 8, 33, 31, 51, 47, 65, 62, 90, 110, 109, 112, 116, 102, 122, 136, 141, 144, 149, 183], length is 23
total value:394.70874210714305
the average time satisfaction is 0.5588753431622718
the variance is 0.20314466708547593
Clock:1685-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/145', 'AS350B3[1,1018,6,0]/-44', 'EC225[0,5744,20,4]/-49', 'H130[0,1050,8,0]/-49']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31.0, -48, -55, -77, -71, -97, -127, -129, -166, -186]; Helicopters=['S-76[1,1545,10,1]/-30']
position_2杭州市桐庐县: Tasks=[-13, -33.0, -68, -128, -188, -200]; Helicopters=['Bell412[1,1183,12,0]/-7']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62.0, -86, -102.0, -165, -198]; Helicopters=['EC145[0,1793,3,0]/-36', 'AW119Kx[0,1469,0,1]/-21']
position_6台州市天台县: Tasks=[-10.0, -37, -58, -44, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-9']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47.0, -84, -119, -103, -108, -152, -177, -179, -197, -183.0, -187]; Helicopters=['AW139[0,2778,5,0]/-27', '超黄蜂[1,3000,10,5]/-81']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149.0, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -1, -8.0, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -174, -169, -178, -164, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -132, -122.0, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['H135[1,1021,5,2]/10']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65.0, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,8,1]/1']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 8, 33, 31, 51, 47, 65, 62, 90, 110, 109, 112, 116, 102, 122, 136, 141, 144, 149, 183], length is 23
total value:394.70874210714305
the average time satisfaction is 0.5588753431622718
the variance is 0.20314466708547593
Clock:1685-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/145', 'AS350B3[1,1018,6,0]/-44', 'EC225[0,5744,20,4]/-49', 'H130[0,1050,8,0]/-49']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31.0, -48, -55, -77, -71, -97, -127, -129, -166, -186]; Helicopters=['S-76[1,1545,10,1]/-30']
position_2杭州市桐庐县: Tasks=[-13, -33.0, -68, -128, -188, -200]; Helicopters=['Bell412[1,1183,12,0]/-7']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62.0, -86, -102.0, -165, -198]; Helicopters=['EC145[0,1793,3,0]/-36', 'AW119Kx[0,1469,0,1]/-21']
position_6台州市天台县: Tasks=[-10.0, -37, -58, -44, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-9']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47.0, -84, -119, -103, -108, -152, -177, -179, -197, -183.0, -187]; Helicopters=['AW139[0,2778,5,0]/-27', '超黄蜂[1,3000,10,5]/-81']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149.0, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -1, -8.0, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -174, -169, -178, -164, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -132, -122.0, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['H135[1,1021,5,2]/10']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65.0, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,8,1]/1']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 8, 33, 31, 51, 47, 65, 62, 90, 110, 109, 112, 116, 102, 122, 136, 141, 144, 149, 183], length is 23
total value:394.70874210714305
the average time satisfaction is 0.5588753431622718
the variance is 0.20314466708547593
Clock:1685-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/145', 'AS350B3[1,1018,6,0]/-44', 'EC225[0,5744,20,4]/-49', 'H130[0,1050,8,0]/-49']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31.0, -48, -55, -77, -71, -97, -127, -129, -166, -186]; Helicopters=['S-76[1,1545,10,1]/-30']
position_2杭州市桐庐县: Tasks=[-13, -33.0, -68, -128, -188, -200]; Helicopters=['Bell412[1,1183,12,0]/-7']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62.0, -86, -102.0, -165, -198]; Helicopters=['EC145[0,1793,3,0]/-36', 'AW119Kx[0,1469,0,1]/-21']
position_6台州市天台县: Tasks=[-10.0, -37, -58, -44, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-9']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47.0, -84, -119, -103, -108, -152, -177, -179, -197, -183.0, -187]; Helicopters=['AW139[0,2778,5,0]/-27', '超黄蜂[1,3000,10,5]/-81']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149.0, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -1, -8.0, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -174, -169, -178, -164, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -132, -122.0, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['H135[1,1021,5,2]/10']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65.0, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,8,1]/1']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 8, 33, 31, 51, 47, 65, 62, 90, 110, 109, 112, 116, 102, 122, 136, 141, 144, 149, 183], length is 23
total value:394.70874210714305
the average time satisfaction is 0.5588753431622718
the variance is 0.20314466708547593
Clock:1685-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/145', 'AS350B3[1,1018,6,0]/-44', 'EC225[0,5744,20,4]/-49', 'H130[0,1050,8,0]/-49']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31.0, -48, -55, -77, -71, -97, -127, -129, -166, -186]; Helicopters=['S-76[1,1545,10,1]/-30']
position_2杭州市桐庐县: Tasks=[-13, -33.0, -68, -128, -188, -200]; Helicopters=['Bell412[1,1183,12,0]/-7']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62.0, -86, -102.0, -165, -198]; Helicopters=['EC145[0,1793,3,0]/-36', 'AW119Kx[0,1469,0,1]/-21']
position_6台州市天台县: Tasks=[-10.0, -37, -58, -44, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-9']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47.0, -84, -119, -103, -108, -152, -177, -179, -197, -183.0, -187]; Helicopters=['AW139[0,2778,5,0]/-27', '超黄蜂[1,3000,10,5]/-81']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149.0, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -1, -8.0, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -174, -169, -178, -164, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -132, -122.0, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['H135[1,1021,5,2]/10']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65.0, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,8,1]/1']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 8, 33, 31, 51, 47, 65, 62, 90, 110, 109, 112, 116, 102, 122, 136, 141, 144, 149, 183], length is 23
total value:394.70874210714305
the average time satisfaction is 0.5588753431622718
the variance is 0.20314466708547593
Clock:1685-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/145', 'AS350B3[1,1018,6,0]/-44', 'EC225[0,5744,20,4]/-49', 'H130[0,1050,8,0]/-49']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31.0, -48, -55, -77, -71, -97, -127, -129, -166, -186]; Helicopters=['S-76[1,1545,10,1]/-30']
position_2杭州市桐庐县: Tasks=[-13, -33.0, -68, -128, -188, -200]; Helicopters=['Bell412[1,1183,12,0]/-7']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62.0, -86, -102.0, -165, -198]; Helicopters=['EC145[0,1793,3,0]/-36', 'AW119Kx[0,1469,0,1]/-21']
position_6台州市天台县: Tasks=[-10.0, -37, -58, -44, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-9']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47.0, -84, -119, -103, -108, -152, -177, -179, -197, -183.0, -187]; Helicopters=['AW139[0,2778,5,0]/-27', '超黄蜂[1,3000,10,5]/-81']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149.0, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -1, -8.0, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -174, -169, -178, -164, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -132, -122.0, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['H135[1,1021,5,2]/10']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65.0, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,8,1]/1']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 8, 33, 31, 51, 47, 65, 62, 90, 110, 109, 112, 116, 102, 122, 136, 141, 144, 149, 183], length is 23
total value:394.70874210714305
the average time satisfaction is 0.5588753431622718
the variance is 0.20314466708547593
Clock:1685-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/145', 'AS350B3[1,1018,6,0]/-44', 'EC225[0,5744,20,4]/-49', 'H130[0,1050,8,0]/-49']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31.0, -48, -55, -77, -71, -97, -127, -129, -166, -186]; Helicopters=['S-76[1,1545,10,1]/-30']
position_2杭州市桐庐县: Tasks=[-13, -33.0, -68, -128, -188, -200]; Helicopters=['Bell412[1,1183,12,0]/-7']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62.0, -86, -102.0, -165, -198]; Helicopters=['EC145[0,1793,3,0]/-36', 'AW119Kx[0,1469,0,1]/-21']
position_6台州市天台县: Tasks=[-10.0, -37, -58, -44, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-9']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47.0, -84, -119, -103, -108, -152, -177, -179, -197, -183.0, -187]; Helicopters=['AW139[0,2778,5,0]/-27', '超黄蜂[1,3000,10,5]/-81']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149.0, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -1, -8.0, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -174, -169, -178, -164, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -132, -122.0, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['H135[1,1021,5,2]/10']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65.0, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,8,1]/1']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 8, 33, 31, 51, 47, 65, 62, 90, 110, 109, 112, 116, 102, 122, 136, 141, 144, 149, 183], length is 23
total value:394.70874210714305
the average time satisfaction is 0.5588753431622718
the variance is 0.20314466708547593
Clock:1685-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/145', 'AS350B3[1,1018,6,0]/-44', 'EC225[0,5744,20,4]/-49', 'H130[0,1050,8,0]/-49']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31.0, -48, -55, -77, -71, -97, -127, -129, -166, -186]; Helicopters=['S-76[1,1545,10,1]/-30']
position_2杭州市桐庐县: Tasks=[-13, -33.0, -68, -128, -188, -200]; Helicopters=['Bell412[1,1183,12,0]/-7']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62.0, -86, -102.0, -165, -198]; Helicopters=['EC145[0,1793,3,0]/-36', 'AW119Kx[0,1469,0,1]/-21']
position_6台州市天台县: Tasks=[-10.0, -37, -58, -44, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-9']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47.0, -84, -119, -103, -108, -152, -177, -179, -197, -183.0, -187]; Helicopters=['AW139[0,2778,5,0]/-27', '超黄蜂[1,3000,10,5]/-81']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149.0, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -1, -8.0, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -174, -169, -178, -164, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -132, -122.0, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['H135[1,1021,5,2]/10']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65.0, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,8,1]/1']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 8, 33, 31, 51, 47, 65, 62, 90, 110, 109, 112, 116, 102, 122, 136, 141, 144, 149, 183], length is 23
total value:394.70874210714305
the average time satisfaction is 0.5588753431622718
the variance is 0.20314466708547593
Clock:1685-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/145', 'AS350B3[1,1018,6,0]/-44', 'EC225[0,5744,20,4]/-49', 'H130[0,1050,8,0]/-49']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31.0, -48, -55, -77, -71, -97, -127, -129, -166, -186]; Helicopters=['S-76[1,1545,10,1]/-30']
position_2杭州市桐庐县: Tasks=[-13, -33.0, -68, -128, -188, -200]; Helicopters=['Bell412[1,1183,12,0]/-7']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62.0, -86, -102.0, -165, -198]; Helicopters=['EC145[0,1793,3,0]/-36', 'AW119Kx[0,1469,0,1]/-21']
position_6台州市天台县: Tasks=[-10.0, -37, -58, -44, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-9']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47.0, -84, -119, -103, -108, -152, -177, -179, -197, -183.0, -187]; Helicopters=['AW139[0,2778,5,0]/-27', '超黄蜂[1,3000,10,5]/-81']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149.0, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -1, -8.0, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -174, -169, -178, -164, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -132, -122.0, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['H135[1,1021,5,2]/10']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65.0, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,8,1]/1']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 8, 33, 31, 51, 47, 65, 62, 90, 110, 109, 112, 116, 102, 122, 136, 141, 144, 149, 183], length is 23
total value:394.70874210714305
the average time satisfaction is 0.5588753431622718
the variance is 0.20314466708547593
Clock:1685-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/145', 'AS350B3[1,1018,6,0]/-44', 'EC225[0,5744,20,4]/-49', 'H130[0,1050,8,0]/-49']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31.0, -48, -55, -77, -71, -97, -127, -129, -166, -186]; Helicopters=['S-76[1,1545,10,1]/-30']
position_2杭州市桐庐县: Tasks=[-13, -33.0, -68, -128, -188, -200]; Helicopters=['Bell412[1,1183,12,0]/-7']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62.0, -86, -102.0, -165, -198]; Helicopters=['EC145[0,1793,3,0]/-36', 'AW119Kx[0,1469,0,1]/-21']
position_6台州市天台县: Tasks=[-10.0, -37, -58, -44, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-9']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47.0, -84, -119, -103, -108, -152, -177, -179, -197, -183.0, -187]; Helicopters=['AW139[0,2778,5,0]/-27', '超黄蜂[1,3000,10,5]/-81']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149.0, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -1, -8.0, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -174, -169, -178, -164, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -132, -122.0, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['H135[1,1021,5,2]/10']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65.0, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,8,1]/1']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 8, 33, 31, 51, 47, 65, 62, 90, 110, 109, 112, 116, 102, 122, 136, 141, 144, 149, 183], length is 23
total value:394.70874210714305
the average time satisfaction is 0.5588753431622718
the variance is 0.20314466708547593
Clock:1685-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/145', 'AS350B3[1,1018,6,0]/-44', 'EC225[0,5744,20,4]/-49', 'H130[0,1050,8,0]/-49']
position_1杭州市富阳区: Tasks=[-7, -23, -40, -31.0, -48, -55, -77, -71, -97, -127, -129, -166, -186]; Helicopters=['S-76[1,1545,10,1]/-30']
position_2杭州市桐庐县: Tasks=[-13, -33.0, -68, -128, -188, -200]; Helicopters=['Bell412[1,1183,12,0]/-7']
position_3丽水市缙云县: Tasks=[-64, -157, -171]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -50, -41, -54, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62.0, -86, -102.0, -165, -198]; Helicopters=['EC145[0,1793,3,0]/-36', 'AW119Kx[0,1469,0,1]/-21']
position_6台州市天台县: Tasks=[-10.0, -37, -58, -44, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-9']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47.0, -84, -119, -103, -108, -152, -177, -179, -197, -183.0, -187]; Helicopters=['AW139[0,2778,5,0]/-27', '超黄蜂[1,3000,10,5]/-81']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=[]
position_12金华市义乌市: Tasks=[-29, -34, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -110.0, -109.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149.0, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -1, -8.0, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -174, -169, -178, -164, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=[]
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -132, -122.0, -148, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -63, -80, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['H135[1,1021,5,2]/10']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65.0, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -9, -38, -32, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,8,1]/1']
position_26宁波市海曙区: Tasks=[-15.0, -14, -4, -49, -74, -124, -139, -138, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 8, 33, 31, 51, 47, 65, 62, 90, 110, 109, 112, 116, 102, 122, 136, 141, 144, 149, 183], length is 23
total value:394.70874210714305
the average time satisfaction is 0.5588753431622718
the variance is 0.20314466708547593
  - 注意：未能从环境中获取时间满意度数据
  - LLM-Tuned RL simulation finished after 12609 steps.
  - LLM-Tuned RL 策略获得效益: 394.71
Running Reward-Tuned RL Strategy...
  - Model 'rlhf_7685_15e6_7685_5e6_v3_400' loaded. Starting simulation...
  ... simulation step 1000
  ... simulation step 2000
  ... simulation step 3000
  ... simulation step 4000
  ... simulation step 5000
  ... simulation step 6000
  ... simulation step 7000
  ... simulation step 8000
  ... simulation step 9000
  ... simulation step 10000
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
Clock:1684-----------------
position_0萧山机场: Tasks=[]; Helicopters=['Bell429[0,1371,8,0]/-44', 'EC225[0,5744,20,4]/-50', 'H135[1,1455,5,2]/-56']
position_1杭州市富阳区: Tasks=[-7, -23, -31, -40, -48, -55, -77.0, -71, -97, -127, -129, -166, -186]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-13, -33, -68, -128, -200, -188]; Helicopters=['S-76[1,2129,8,2]/-77']
position_3丽水市缙云县: Tasks=[-64, -157, -171.0]; Helicopters=[]
position_4台州市临海市: Tasks=[-28, -41, -54, -50, -75, -131, -150, -167]; Helicopters=[]
position_5台州市仙居县: Tasks=[-5, -59, -62, -86, -102.0, -165, -198]; Helicopters=['超黄蜂[1,3000,10,2]/-45']
position_6台州市天台县: Tasks=[-10.0, -37, -44, -58, -67, -66, -107, -144.0, -154]; Helicopters=[]
position_7台州市三门县: Tasks=[-18, -25, -76, -95, -123, -142, -162, -199]; Helicopters=['AW109SP[1,1515,6,0]/-10']
position_8舟山市嵊泗县: Tasks=[-35, -30, -47, -84, -119, -103, -108, -152, -179, -177.0, -197, -183, -187]; Helicopters=['AW139[0,2778,13,2]/8', 'EC145[0,1209,3,1]/-1', 'Bell412[1,1408,6,0]/-16']
position_9舟山市岱山县: Tasks=[-85, -100, -115, -151, -173, -195]; Helicopters=[]
position_10舟山市定海区: Tasks=[-2, -19.0, -143, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-3, -21, -24, -53, -78, -137, -146, -191]; Helicopters=['H130[0,350,8,0]/-10']
position_12金华市义乌市: Tasks=[-34, -29, -106, -125, -193]; Helicopters=[]
position_13金华市磐安县: Tasks=[-17.0, -56, -81, -109.0, -110.0, -145, -156]; Helicopters=[]
position_14金华市浦江县: Tasks=[-112.0, -126, -149, -184]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-16, -8, -1, -26, -45, -61, -101, -141.0]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-39, -36, -52, -92, -91, -114, -120, -133, -180.0]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-51.0, -94, -116.0, -140, -176]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-22, -73, -96, -147, -159, -160, -163, -189]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-83, -104, -118]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-11, -60, -70, -98, -178, -169, -164, -174, -196]; Helicopters=[]
position_21宁波市宁海县: Tasks=[-46, -105, -135, -172, -185]; Helicopters=['AW119Kx[0,1469,0,2]/-27']
position_22宁波市象山县: Tasks=[-72, -90.0, -117, -122, -132, -148.0, -155]; Helicopters=[]
position_23宁波市奉化区: Tasks=[-12, -80, -63, -99, -111, -113, -134, -158, -175, -181]; Helicopters=['AS350B3[1,613,6,0]/1']
position_24宁波市鄞州区: Tasks=[-6, -27, -43, -79, -65, -87, -88, -136.0, -153]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-9, -20, -32, -38, -42, -57, -69, -82, -93, -89, -121, -168, -161, -194, -190, -182, -192]; Helicopters=['H145[1,1793,0,1]/-4']
position_26宁波市海曙区: Tasks=[-14, -15.0, -4, -49, -74, -124, -138, -139, -130]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[15, 19, 10, 17, 51, 77, 90, 109, 110, 112, 116, 102, 136, 141, 148, 144, 180, 177, 171], length is 19
total value:400.4480118468438
the average time satisfaction is 0.5642351836397782
the variance is 0.19999583724481668
  - 注意：未能从环境中获取时间满意度数据
  - Reward-Tuned RL simulation finished after 10719 steps.
  - Reward-Tuned RL 策略获得效益: 400.45

--- 运行评估集 2/10 (种子: 86) ---
Running Nearby Strategy...
[16, 54, 47, 66, 76, 85, 145]
7
  - 注意：未能从环境中获取时间满意度数据
  - 临近策略 (Nearby) 获得效益: 411.52
Running Best-fit Strategy...
[16, 6, 35, 54, 66, 76, 85, 145]
8
  - 注意：未能从环境中获取时间满意度数据
  - 最优匹配策略 (Best-fit) 获得效益: 418.44
Running Base RL Strategy...
  - Model 'nohf_7685_15e6' loaded. Starting simulation...
  ... simulation step 1000
  ... simulation step 2000
  ... simulation step 3000
  ... simulation step 4000
  ... simulation step 5000
  ... simulation step 6000
  ... simulation step 7000
  ... simulation step 8000
  ... simulation step 9000
  ... simulation step 10000
  ... simulation step 11000
Clock:1674-----------------
position_0萧山机场: Tasks=[]; Helicopters=['EC225[0,5744,20,4]/97', 'AS350B3[1,1018,6,0]/-47', 'H130[0,1050,8,0]/-51', 'H145[1,1793,8,1]/-60']
position_1杭州市富阳区: Tasks=[-17, -157, -152]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -58, -56, -53, -80, -140, -137, -156, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14, -24, -54.0, -98, -125, -136, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44, -86, -159, -188]; Helicopters=['Bell429[0,1287,8,0]/-4']
position_5台州市仙居县: Tasks=[-5, -35.0, -99, -96, -132, -138, -153, -179, -174, -177, -192, -184, -190, -195]; Helicopters=['AW109SP[1,1515,8,0]/-19']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -168, -161]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -45, -41, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['S-76[1,2129,10,0]/-7', 'EC145[0,1793,3,0]/-61', 'AW119Kx[0,1600,0,1]/-43']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -49, -51, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['Bell412[1,1408,5,0]/-51']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-11, -15, -40, -63, -109, -102, -110, -180, -197]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -147, -141, -191, -187]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,2]/10']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -186, -183]; Helicopters=['超黄蜂[1,3000,10,2]/-8']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW139[0,1966,13,1]/-24']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-2, -12, -21, -65, -105, -135, -178, -194]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 35, 54, 66, 76, 85, 116, 145], length is 8
total value:423.2472108636599
the average time satisfaction is 0.699130632248812
the variance is 0.17303662495528677
Clock:1674-----------------
position_0萧山机场: Tasks=[]; Helicopters=['EC225[0,5744,20,4]/97', 'AS350B3[1,1018,6,0]/-47', 'H130[0,1050,8,0]/-51', 'H145[1,1793,8,1]/-60']
position_1杭州市富阳区: Tasks=[-17, -157, -152]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -58, -56, -53, -80, -140, -137, -156, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14, -24, -54.0, -98, -125, -136, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44, -86, -159, -188]; Helicopters=['Bell429[0,1287,8,0]/-4']
position_5台州市仙居县: Tasks=[-5, -35.0, -99, -96, -132, -138, -153, -179, -174, -177, -192, -184, -190, -195]; Helicopters=['AW109SP[1,1515,8,0]/-19']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -168, -161]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -45, -41, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['S-76[1,2129,10,0]/-7', 'EC145[0,1793,3,0]/-61', 'AW119Kx[0,1600,0,1]/-43']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -49, -51, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['Bell412[1,1408,5,0]/-51']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-11, -15, -40, -63, -109, -102, -110, -180, -197]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -147, -141, -191, -187]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,2]/10']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -186, -183]; Helicopters=['超黄蜂[1,3000,10,2]/-8']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW139[0,1966,13,1]/-24']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-2, -12, -21, -65, -105, -135, -178, -194]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 35, 54, 66, 76, 85, 116, 145], length is 8
total value:423.2472108636599
the average time satisfaction is 0.699130632248812
the variance is 0.17303662495528677
Clock:1674-----------------
position_0萧山机场: Tasks=[]; Helicopters=['EC225[0,5744,20,4]/97', 'AS350B3[1,1018,6,0]/-47', 'H130[0,1050,8,0]/-51', 'H145[1,1793,8,1]/-60']
position_1杭州市富阳区: Tasks=[-17, -157, -152]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -58, -56, -53, -80, -140, -137, -156, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14, -24, -54.0, -98, -125, -136, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44, -86, -159, -188]; Helicopters=['Bell429[0,1287,8,0]/-4']
position_5台州市仙居县: Tasks=[-5, -35.0, -99, -96, -132, -138, -153, -179, -174, -177, -192, -184, -190, -195]; Helicopters=['AW109SP[1,1515,8,0]/-19']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -168, -161]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -45, -41, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['S-76[1,2129,10,0]/-7', 'EC145[0,1793,3,0]/-61', 'AW119Kx[0,1600,0,1]/-43']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -49, -51, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['Bell412[1,1408,5,0]/-51']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-11, -15, -40, -63, -109, -102, -110, -180, -197]; Helicopters=[]
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -147, -141, -191, -187]; Helicopters=[]
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,2]/10']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -186, -183]; Helicopters=['超黄蜂[1,3000,10,2]/-8']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW139[0,1966,13,1]/-24']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-2, -12, -21, -65, -105, -135, -178, -194]; Helicopters=[]
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 35, 54, 66, 76, 85, 116, 145], length is 8
total value:423.2472108636599
the average time satisfaction is 0.699130632248812
the variance is 0.17303662495528677
  - 注意：未能从环境中获取时间满意度数据
  - Base RL simulation finished after 11259 steps.
  - Base RL 策略获得效益: 423.25
Running LLM-Tuned RL Strategy...
  - Model 'rlhf_7685_15e6_7685_1e6_v3_394' loaded. Starting simulation...
  ... simulation step 1000
  ... simulation step 2000
  ... simulation step 3000
  ... simulation step 4000
  ... simulation step 5000
  ... simulation step 6000
  ... simulation step 7000
  ... simulation step 8000
  ... simulation step 9000
  ... simulation step 10000
  ... simulation step 11000
  ... simulation step 12000
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
Clock:1659-----------------
position_0萧山机场: Tasks=[]; Helicopters=[]
position_1杭州市富阳区: Tasks=[-17, -152, -157]; Helicopters=[]
position_2杭州市桐庐县: Tasks=[-16.0, -32, -28, -53, -58, -56, -80, -137, -140, -156.0, -198]; Helicopters=[]
position_3丽水市缙云县: Tasks=[-14.0, -24, -54.0, -98, -136, -125, -146, -166]; Helicopters=[]
position_4台州市临海市: Tasks=[-44.0, -86, -159, -188]; Helicopters=['Bell429[0,1019,8,0]/-21']
position_5台州市仙居县: Tasks=[-5, -35.0, -96, -99, -132, -138, -153, -174, -177, -179, -184, -192, -190, -195]; Helicopters=['EC145[0,1603,3,2]/-18', 'Bell412[1,1292,14,0]/-39']
position_6台州市天台县: Tasks=[-9, -43, -92, -117, -113, -161, -168]; Helicopters=[]
position_7台州市三门县: Tasks=[-6, -41, -45, -103, -106, -145.0, -171, -193]; Helicopters=[]
position_8舟山市嵊泗县: Tasks=[-36, -31, -66.0, -87, -126, -189]; Helicopters=['超黄蜂[1,3000,10,0]/-62']
position_9舟山市岱山县: Tasks=[-165, -167]; Helicopters=[]
position_10舟山市定海区: Tasks=[-29, -27, -51, -49, -70, -71, -73, -89, -116.0, -111, -114, -170]; Helicopters=[]
position_11金华市东阳市: Tasks=[-30, -25, -42, -68, -62, -129, -175]; Helicopters=[]
position_12金华市义乌市: Tasks=[-7, -38, -84, -143, -162]; Helicopters=[]
position_13金华市磐安县: Tasks=[-4, -60, -76.0, -74, -185]; Helicopters=['AW139[0,2778,4,2]/-65']
position_14金华市浦江县: Tasks=[-33, -48, -79, -75, -72, -112, -107, -120, -139, -155, -151, -163]; Helicopters=[]
position_15绍兴市嵊州市: Tasks=[-15, -11, -40, -63, -110, -109, -102, -180, -197]; Helicopters=['AS350B3[1,1018,6,0]/-9']
position_16绍兴市诸暨市: Tasks=[-23, -88, -95, -90, -130, -148]; Helicopters=[]
position_17绍兴市新昌县: Tasks=[-47, -118, -133, -121, -128, -173, -181]; Helicopters=[]
position_18绍兴市上虞区: Tasks=[-1, -19, -10, -64, -91, -83, -131, -169]; Helicopters=[]
position_19绍兴市柯桥区: Tasks=[-3, -18, -39, -59, -101, -115, -122, -123, -141, -147, -191, -187]; Helicopters=['H130[0,1050,8,0]/2']
position_20宁波市余姚市: Tasks=[-8, -37, -77, -67, -150, -158, -200]; Helicopters=['H135[1,1455,5,0]/-1']
position_21宁波市宁海县: Tasks=[-52, -69, -94, -108, -127, -160, -176, -182, -183, -186.0]; Helicopters=['S-76[1,2129,10,0]/-6', 'AW109SP[1,1515,4,0]/-5']
position_22宁波市象山县: Tasks=[-34, -26, -85.0, -154, -149, -196]; Helicopters=['AW119Kx[0,1094,0,1]/-19']
position_23宁波市奉化区: Tasks=[-13, -22, -50, -57, -81, -97, -119, -104, -124, -172]; Helicopters=[]
position_24宁波市鄞州区: Tasks=[-12, -2, -21, -65, -105, -135, -178.0, -194]; Helicopters=['H145[1,1793,8,1]/1', 'EC225[0,5744,20,3]/-15']
position_25宁波市北仑区: Tasks=[-20, -55, -61, -78, -93, -144, -199]; Helicopters=[]
position_26宁波市海曙区: Tasks=[-46, -100, -82, -134, -142, -164]; Helicopters=[]
all tasks have done -----------------
the delete tasks are these:[16, 14, 35, 54, 44, 66, 76, 85, 116, 145, 156, 178, 186], length is 13
total value:411.78496821533855
the average time satisfaction is 0.656544766467513
the variance is 0.18977759537638508
  - 注意：未能从环境中获取时间满意度数据
  - LLM-Tuned RL simulation finished after 12123 steps.
  - LLM-Tuned RL 策略获得效益: 411.78
Running Reward-Tuned RL Strategy...
  - Model 'rlhf_7685_15e6_7685_5e6_v3_400' loaded. Starting simulation...
  ... simulation step 1000
