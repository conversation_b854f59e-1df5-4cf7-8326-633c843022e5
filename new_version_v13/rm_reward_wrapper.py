"""
RM 奖励整合 Wrapper（最小示例）
- 将环境即时奖励 r_env 与奖励模型输出 r_rm 融合：r_total = r_env + lambda_rm * r_rm
- 适配 PettingZoo AEC 接口：last()/step() 五元组返回
- 依赖：torch、reward_model.py 训练得到的 state_dict（可选）

注意：示例为演示结构，部署到大规模训练需按实际维度/特征工程对齐。
"""
from __future__ import annotations
from typing import Any, Dict, Tuple
import numpy as np
import torch

# 这里直接引入本仓的奖励模型结构
from reward_model import rewardModel


class RMRewardWrapper:
    def __init__(self, env, model_path: str | None = None, lambda_rm: float = 0.1, device: str = "cpu"):
        self.env = env
        self.lambda_rm = float(lambda_rm)
        self.device = torch.device(device)
        # 简化假设：context_size 与 response_size 由外部配置或模型加载时确定
        # 若提供了已训练权重，则需要使用与训练时一致的尺寸。
        self.rm = None
        if model_path:
            try:
                # 由于 reward_model.py 中保存了 state_dict，需要外部告知尺寸；
                # 这里演示用一个经验尺寸（需与训练数据对齐）。
                context_size = 898
                response_size = 1
                self.rm = rewardModel(context_size, response_size).to(self.device)
                self.rm.load_state_dict(torch.load(model_path, map_location=self.device))
                self.rm.eval()
            except Exception as e:
                print(f"[RMWrapper] 加载奖励模型失败：{e}，将仅使用环境奖励。")
                self.rm = None

    # 代理属性/方法
    @property
    def possible_agents(self):
        return self.env.possible_agents

    def observation_space(self, agent):
        return self.env.observation_space(agent)

    def action_space(self, agent):
        return self.env.action_space(agent)

    def reset(self, *args, **kwargs):
        return self.env.reset(*args, **kwargs)

    def agent_iter(self, *args, **kwargs):
        return self.env.agent_iter(*args, **kwargs)

    def last(self) -> Tuple[Any, float, bool, bool, Dict[str, Any]]:
        obs, rew, terminated, truncated, info = self.env.last()
        # 在 last() 阶段不改写奖励，推迟到 step 後写回（SB3/PZ 的迭代流程会在 step 后累计）
        self._last_obs = obs
        self._last_info = info
        self._last_env_rew = float(rew)
        return obs, rew, terminated, truncated, info

    def _infer_rm_reward(self, obs: Any, info: Dict[str, Any], action: Any) -> float:
        if self.rm is None:
            return 0.0
        try:
            # 构造最小 context 向量（示范：仅使用 obs 展平）。
            # 若需复现实验，应与 reward_model.py 的 collate_fn 一致。
            obs_arr = np.array(obs, dtype=np.float32).reshape(-1)
            context = torch.tensor(obs_arr, dtype=torch.float32, device=self.device).unsqueeze(0)
            act_val = float(action) if action is not None else 0.0
            response = torch.tensor([act_val], dtype=torch.float32, device=self.device).unsqueeze(0)
            with torch.no_grad():
                out = self.rm(context, response).item()
            return float(out)
        except Exception as e:
            # 容错：若推理失败，忽略 RM 奖励
            return 0.0

    def step(self, action):
        # 委托底层环境执行
        self.env.step(action)
        # 读取 step 后的最新状态/奖励
        obs, rew, terminated, truncated, info = self.env.last()
        # 基于上一步的观测/信息与本步动作，估算 RM 奖励
        r_rm = self._infer_rm_reward(getattr(self, "_last_obs", obs), getattr(self, "_last_info", info), action)
        r_total = float(rew) + self.lambda_rm * r_rm
        # 写回改写奖励到 info 以便日志记录；注意 PettingZoo 原生奖励是逐 agent 内部存储的
        info = dict(info or {})
        info["reward_env"] = float(rew)
        info["reward_rm"] = float(r_rm)
        info["reward_total"] = float(r_total)
        # 返回改写后的五元组
        return obs, r_total, terminated, truncated, info

    def close(self):
        return self.env.close()

