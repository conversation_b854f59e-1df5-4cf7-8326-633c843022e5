"""
基于 RM 奖励整合的训练示例（最小可跑草案）
- 在 aerta_position_v13_new 环境上，使用 RMRewardWrapper 融合 RM 奖励
- 依赖：supersuit、stable-baselines3、reward_model.py（可选，使用 state_dict）
- 注意：此示例仅示范结构，RM 的特征工程需与 reward_model.py 中 collate_fn 对齐以复现实验
"""
from __future__ import annotations
import numpy as np
import random

from stable_baselines3 import PPO
import supersuit as ss
from pettingzoo.utils import to_parallel

from aerta_position_v13_new import env as aerta_position_v13_env, Position, Helicopter, Task
from rm_reward_wrapper import RMRewardWrapper


# 简化的测试数据
positions = [Position(0, "萧山机场", 1), Position(1, "杭州市富阳区", 1)]
helicopters = [Helicopter(1, 'EC145', 0, 1, 0, 1793, 3, 2, 680, 246, 0)]
edge = [[0, 57], [57, 0]]
positions[0].helicopters = [1]


def generate_simple_tasks():
    tasks = []
    for i in range(5):
        task = Task(i+1, i+1, 0, 0, 1, 1, 0, 0, 0, [10, 20, 30, 0.5])
        tasks.append(task)
    return tasks


def main():
    tasks = generate_simple_tasks()

    # 基础 PZ 环境
    env = aerta_position_v13_env(positions=positions, helicopters=helicopters, tasks=tasks, edge=edge)

    # 叠加 RM 奖励（如有训练好的 state_dict 路径，填到 model_path 参数中）
    env = RMRewardWrapper(env, model_path=None, lambda_rm=0.1, device="cpu")

    # 适配 SB3 训练流水线
    env = to_parallel(env)
    env = ss.pad_action_space_v0(env)
    env = ss.pettingzoo_env_to_vec_env_v1(env)
    env = ss.concat_vec_envs_v1(env, 1, base_class='stable_baselines3')

    model = PPO("MlpPolicy", env, verbose=1, n_steps=64, batch_size=32)
    print("开始融合 RM 奖励的训练...")
    model.learn(total_timesteps=2000)
    model.save("./test_model_v13_rm")
    print("训练完成，模型已保存为 test_model_v13_rm.zip")

    env.close()


if __name__ == '__main__':
    main()

