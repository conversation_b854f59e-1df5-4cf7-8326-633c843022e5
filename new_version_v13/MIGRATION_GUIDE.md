# 代码迁移指南：从Python 3.7 + 旧版PettingZoo到新版本

## 概述

本指南详细说明如何将基于Python 3.7和旧版本PettingZoo的直升机任务分配强化学习系统迁移到现代版本。

## 版本要求

### 旧版本
- Python 3.7
- PettingZoo < 1.24
- Stable-Baselines3 < 2.0
- Gym < 0.26

### 新版本目标
- Python 3.9+
- PettingZoo >= 1.24.0
- Stable-Baselines3 >= 2.0.0
- Gymnasium >= 0.29.1
- PyTorch >= 2.3.0

## 主要变更点

### 1. 导入语句变更

**旧版本：**
```python
import gym
from gym import spaces
from stable_baselines3 import PPO
from pettingzoo import AECEnv
```

**新版本：**
```python
import gymnasium as gym
from gymnasium import spaces
from stable_baselines3 import PPO
from pettingzoo import AECEnv
```

### 2. 环境创建变更

**旧版本：**
```python
env = gym.make('CartPole-v1')
```

**新版本：**
```python
env = gym.make('CartPole-v1', render_mode='human')  # 需要显式指定render_mode
```

### 3. PettingZoo环境变更

**主要保持兼容，但需要注意：**
- 导入路径可能有变化
- 某些方法签名可能有微调
- 观察空间和动作空间的处理更严格

### 4. Stable-Baselines3变更

**重要的默认参数变更：**

**TD3/DDPG：**
```python
# 旧版本默认
model = TD3("MlpPolicy", env, train_freq=(1, "episode"), gradient_steps=-1, batch_size=100)

# 新版本默认
model = TD3("MlpPolicy", env, train_freq=1, gradient_steps=1, batch_size=256)
```

**DQN：**
```python
# 旧版本默认
model = DQN("MlpPolicy", env, learning_starts=50_000)

# 新版本默认
model = DQN("MlpPolicy", env, learning_starts=100)
```

## 迁移步骤

### 步骤1：环境准备
1. 升级Python到3.9+
2. 创建新的虚拟环境
3. 安装新版本依赖：`pip install -r requirements_new.txt`

### 步骤2：代码修改
1. 更新所有导入语句
2. 修改环境创建代码
3. 调整超参数以匹配新默认值
4. 更新类型提示

### 步骤3：测试验证
1. 运行单元测试
2. 验证环境功能
3. 检查训练性能
4. 确认模型保存/加载

## 具体文件修改清单

### aerta_position_V12.py
- [ ] 更新导入语句
- [ ] 检查PettingZoo AECEnv接口
- [ ] 验证观察空间和动作空间定义
- [ ] 测试环境重置和步进逻辑

### 训练脚本
- [ ] 更新Stable-Baselines3导入
- [ ] 调整超参数设置
- [ ] 更新回调函数
- [ ] 修改模型保存/加载逻辑

### 评估脚本
- [ ] 更新环境创建
- [ ] 修改模型加载
- [ ] 调整评估逻辑

## 常见问题和解决方案

### 1. 导入错误
**问题：** `ModuleNotFoundError: No module named 'gym'`
**解决：** 将所有`import gym`改为`import gymnasium as gym`

### 2. 环境创建失败
**问题：** 环境创建时缺少render_mode参数
**解决：** 显式指定render_mode参数

### 3. 超参数性能下降
**问题：** 使用新默认超参数后性能下降
**解决：** 参考旧版本超参数，逐步调整

### 4. 类型检查错误
**问题：** 新版本类型检查更严格
**解决：** 更新类型提示，确保类型一致性

## 测试策略

1. **单元测试：** 确保每个组件独立工作
2. **集成测试：** 验证整个训练流程
3. **性能测试：** 对比新旧版本的训练效果
4. **兼容性测试：** 确保模型可以正确保存和加载

## 回滚计划

如果迁移过程中遇到无法解决的问题：
1. 保留原始代码备份
2. 记录所有修改内容
3. 准备快速回滚到旧版本的方案
4. 逐步迁移，而非一次性全部更改 