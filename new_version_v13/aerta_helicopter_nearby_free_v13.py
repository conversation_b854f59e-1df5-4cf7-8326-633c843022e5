from copy import deepcopy
import heapq
import numpy as np 
import gymnasium as gym
from gymnasium import spaces
from gymnasium.utils import seeding
from pettingzoo import AECEnv
from pettingzoo.utils import agent_selector, wrappers
import math

inf = 10000

def env(**kwargs):
    env = raw_env(**kwargs)
    env = wrappers.CaptureStdoutWrapper(env)
    env = wrappers.OrderEnforcingWrapper(env)
    return env

# 保持原有的Position, Helicopter, Task类定义不变，只更新导入
class Position:
    def __init__(self, number, name, is_landed):
        self.number = number
        self.name = name
        self.is_landed = is_landed
        self.helicopters = []
        self.tasks = []
    
    def __eq__(self, other):
        return self.number == other.number

class Helicopter:
    def __init__(self, No, Name, position_number, can_hover, search_capacity, 
                 material_remain, seat_remain, medical_remain, voyage_remain, v, busy_duration):
        self.number = No
        self.Name = Name
        self.position_number = position_number
        self.can_hover = can_hover
        self.search_capacity = search_capacity
        self.material_remain = material_remain
        self.seat_remain = seat_remain
        self.medical_remain = medical_remain
        self.voyage_remain = voyage_remain
        self.v = v
        self.busy_duration = busy_duration
    
    def __eq__(self, other):
        return self.number == other.number

class Task:
    def __init__(self, No, publish_clock, tw, td, position_number, search_demand, 
                 material_demand, person_deliver, medical_demand, m):
        self.number = No
        self.publish_clock = publish_clock
        self.tw = tw
        self.td = td
        self.position_number = position_number
        self.search_demand = search_demand
        self.material_demand = material_demand
        self.person_deliver = person_deliver
        self.medical_demand = medical_demand
        self.m = m
        self.priority = self.calculate_residual_priority(0)
        self.finish_clock = -1
        self.finisher = ''
    
    def __lt__(self, other):
        return self.publish_clock < other.publish_clock
    
    def calculate_residual_priority(self, t):
        tk1 = self.m[0]
        tk2 = self.m[1]
        tkmax = self.m[2]
        rk0 = self.m[3]
        t = t - self.publish_clock
        if t <= tk1:
            f = 0
        elif t < tk2:
            f = (2*t - 2*tk2) / (math.pow((tk2 - tk1), 2)) * (1 - rk0)
        elif t < tkmax:
            f = (2*tk2 - 2*t) / (math.pow((tkmax - tk2), 2)) * rk0
        else:
            f = inf
        demand = self.search_demand + self.material_demand/100 + self.person_deliver + self.medical_demand
        return f * demand
    
    def calculate_satisfaction(self, t):
        tk1 = self.m[0]
        tk2 = self.m[1]
        tkmax = self.m[2]
        rk0 = self.m[3]
        t = t - self.publish_clock
        if t <= tk1:
            f = 1
        elif t <= tk2:
            f = math.pow((tk2 - t) / (tk2 - tk1), 2) * (1 - rk0) + rk0
        elif t <= tkmax:
            f = -math.pow((t - tk2) / (tkmax - tk2), 2) * rk0 + rk0
        else:
            f = 0
        return f

class raw_env(AECEnv):
    metadata = {
        'render_modes': ['human'],
        'name': "aerta_helicopter_nearby_v13", 
        "is_parallelizable": True
    }
    
    def __init__(self, positions, helicopters, tasks, edge, render_mode=None):
        super().__init__()
        
        self.positions = positions
        self.helicopters = helicopters
        self.tasks = tasks
        self.edge = edge
        self.clock = 0
        self.render_mode = render_mode

        self.tasks_copy = deepcopy(self.tasks)
        self.helicopters_copy = deepcopy(self.helicopters)
        self.positions_copy = deepcopy(self.positions)
        self.edge_copy = deepcopy(self.edge)

        self.possible_agents = ['helicopter_' + str(helicopters[i].number) for i in range(len(helicopters))]
        self.agent_name_mapping = dict(zip(self.possible_agents, list(range(len(helicopters)))))
        
        max_distance = 500
        max_agents = len(helicopters) + 1
        max_task_num = len(tasks) + 1
        
        self.action_spaces = {agent: spaces.Discrete(2) for agent in self.possible_agents}
        ob = spaces.MultiDiscrete([2, 2, 2, 2, max_distance, max_agents, max_task_num])
        self.observation_spaces = {agent: ob for agent in self.possible_agents}

        self.seed()

        self.agents = self.possible_agents[:]
        self._agent_selector = agent_selector(self.agents)
        self.rewards = {agent: 0 for agent in self.possible_agents}
        self._cumulative_rewards = {agent: 0 for agent in self.possible_agents}
        self.dones = {agent: False for agent in self.possible_agents}
        self.infos = {agent: {} for agent in self.possible_agents}

        self.choices = []
        self.delete_task = []
        self.value = 0
        self.satisfaction_list = []

    def observation_space(self, agent):
        return self.observation_spaces[agent]

    def action_space(self, agent):
        return self.action_spaces[agent]

    def seed(self, seed=None):
        if seed is None:
            seed = np.random.randint(0, 2**32 - 1)
        self.np_random = np.random.RandomState(seed)
        return [seed]

    def close(self):
        pass

    def _convert_to_dict(self, list_of_list):
        return dict(zip(self.possible_agents, list_of_list))

    def reset(self, seed=None, options=None):
        if seed is not None:
            self.seed(seed)
            
        self.has_reset = True

        self.tasks_copy = deepcopy(self.tasks)
        self.helicopters_copy = deepcopy(self.helicopters)
        self.positions_copy = deepcopy(self.positions)
        self.edge_copy = deepcopy(self.edge)
        self.clock = 0

        self.agents = self.possible_agents[:]
        self._agent_selector.reinit(self.agents)
        self.agent_selection = self._agent_selector.reset()
        self.rewards = self._convert_to_dict(np.zeros(len(self.possible_agents)))
        self._cumulative_rewards = self._convert_to_dict(np.zeros(len(self.possible_agents)))
        self.dones = self._convert_to_dict([False for _ in range(len(self.possible_agents))])
        self.infos = self._convert_to_dict([{} for _ in range(len(self.possible_agents))])

        self.choices = []
        self.delete_task = []
        self.value = 0
        self.satisfaction_list = []
        
        observations = {agent: self.observe(agent) for agent in self.agents}
        infos = {agent: {} for agent in self.agents}
        return observations, infos

    def render(self, mode=None):
        mode = mode or self.render_mode or "human"
        return self.value, len(self.delete_task)

    # 从这里开始复制原始文件的所有方法，保持逻辑不变
    def calculate(self, task, helicopter):
        search = min(task.search_demand, helicopter.search_capacity)
        material = min(task.material_demand, helicopter.material_remain)
        person = min(task.person_deliver, helicopter.seat_remain)
        medical = min(task.medical_demand, helicopter.medical_remain)
        return search, material, person, medical

    def calculate_time(self, task, helicopter):
        search, material, person, medical = self.calculate(task, helicopter)
        transport_time = self.edge_copy[helicopter.position_number][task.position_number] * 60 // helicopter.v
        if helicopter.busy_duration < 0:
            transport_time += -helicopter.busy_duration
        deliver_time = search * 5 + material // 100 + person * 5 + medical * 10
        return transport_time + deliver_time

    def observe(self, agent):
        helicopter = self.helicopters_copy[self.agent_name_mapping[agent]]
        task = self.get_next_task()
        observation = [0, 0, 0, 0, 0, 0, 0]
        if task == None:
            return observation
        if task.search_demand <= helicopter.search_capacity:
            observation[0] = 1
        if task.material_demand > helicopter.material_remain:
            observation[1] = 1
        if task.person_deliver > helicopter.seat_remain:
            observation[2] = 1
        if task.medical_demand > helicopter.medical_remain:
            observation[3] = 1
        observation[4] = self.edge_copy[helicopter.position_number][task.position_number]
        observation[5] = len(self.agents)
        observation[6] = len(self.t_list) if hasattr(self, 't_list') else 0
        return observation

    def distribute_reward(self, helicopter, task):
        t_time = self.calculate_time(task, helicopter)
        time_satisfaction = task.calculate_satisfaction(self.clock + t_time)
        self.satisfaction_list.append(time_satisfaction)
        search, material, person, medical = self.calculate(task, helicopter)
        deliver = search + material/100 + person + medical
        return time_satisfaction * deliver

    def step(self, action):
        if self.dones[self.agent_selection]:
            return self._was_done_step(action)
        self.task = self.get_next_task()
        if self.task is None:
            self.update_state()
        else:
            agent = self.agent_selection
            if action != 0:
                self.choices.append(agent)
            if self._agent_selector.is_last(): 
                if len(self.choices) != 0:
                    helicopter, agent = self.choose_action(self.task)
                    rew = self.distribute_reward(helicopter, self.task)
                    self.rewards = {agent: rew for agent in self.possible_agents}
                    self.value += rew
                    self.change_state(helicopter, self.task)
                self.update_state()

        self.agent_selection = self._agent_selector.next()
        self._accumulate_rewards()
        self._clear_rewards()
        
        # 新版本返回格式：observation, reward, terminated, truncated, info
        return (self.observe(self.agent_selection), self.rewards[self.agent_selection], 
                self.dones[self.agent_selection], False, self.infos[self.agent_selection])

    # 继续添加其他必要的方法...
    def get_next_task(self):
        self.t_list = []
        for i in range(len(self.tasks_copy)):
            if self.tasks_copy[i].publish_clock <= self.clock:
                self.t_list.append(self.tasks_copy[i])
                if self.tasks_copy[i].number not in self.positions_copy[self.tasks_copy[i].position_number].tasks:
                    self.positions_copy[self.tasks_copy[i].position_number].tasks.append(self.tasks_copy[i].number)
        for task in self.t_list:
            task.priority = task.calculate_residual_priority(self.clock)
            self.delete_0_value_task(task)
        next_t_list = heapq.nsmallest(1, self.t_list, key=lambda s: s.priority)
        if len(next_t_list) > 0:
            return next_t_list[0]
        return None

    def choose_action(self, task):
        agent = self.choices[0]
        helicopter = self.helicopters_copy[self.agent_name_mapping[agent]]
        distance = self.edge_copy[task.position_number][helicopter.position_number]
        for i in range(len(self.choices)):
            heli = self.helicopters_copy[self.agent_name_mapping[self.choices[i]]]
            if self.check_helicopter(task, heli):
                i_distance = self.edge_copy[task.position_number][self.helicopters_copy[self.agent_name_mapping[self.choices[i]]].position_number]
                if distance > i_distance:
                    distance = i_distance
                    agent = self.choices[i]
                    helicopter = self.helicopters_copy[self.agent_name_mapping[agent]]
                elif distance == i_distance and self.helicopters_copy[self.agent_name_mapping[self.choices[i]]].v > helicopter.v:
                    distance = i_distance
                    agent = self.choices[i]
                    helicopter = self.helicopters_copy[self.agent_name_mapping[agent]]
        return helicopter, agent

    def change_state(self, helicopter, task):
        search, material, person, medical = self.calculate(task, helicopter)
        position = self.positions_copy[helicopter.position_number]

        helicopter.busy_duration = -self.calculate_time(task, helicopter)
        helicopter.material_remain = helicopter.material_remain - material
        helicopter.seat_remain = helicopter.seat_remain - person
        helicopter.medical_remain = helicopter.medical_remain - medical
        helicopter.voyage_remain = helicopter.voyage_remain - self.edge_copy[helicopter.position_number][task.position_number]
        helicopter.position_number = task.position_number

        task.td = -helicopter.busy_duration
        task.tw = self.clock - task.publish_clock
        task.search_demand = task.search_demand - search
        task.material_demand = task.material_demand - material
        task.person_deliver = task.person_deliver - person
        task.medical_demand = task.medical_demand - medical
        if task.search_demand + task.material_demand + task.person_deliver + task.medical_demand == 0:
            self.tasks_copy.remove(task)
            self.positions_copy[task.position_number].tasks.remove(task.number)
            self.positions_copy[task.position_number].tasks.append(0 - task.number)
        position.helicopters.remove(helicopter.number)
        self.positions_copy[task.position_number].helicopters.append(helicopter.number)
        self.choices = []

    def update_state(self):
        flag = True
        while flag:
            if len(self.tasks_copy) == 0:
                self.dones = {agent: True for agent in self.agents}
                return 0
            busy_helicopters = 0
            self.clock += 1
            for helicopter in self.helicopters_copy:
                helicopter.busy_duration += 1
                self.back_tracking(helicopter)
                if helicopter.busy_duration < 0:
                    busy_helicopters += 1
                if helicopter.busy_duration == 0:
                    helicopter.seat_remain = deepcopy(self.helicopters[helicopter.number-1].seat_remain)
                    helicopter.medical_remain = deepcopy(self.helicopters[helicopter.number-1].medical_remain)
            if busy_helicopters == len(self.helicopters_copy):
                continue

            next_task = self.get_next_task()
            if next_task == None:
                continue
            
            self.agents = []
            task = self.get_next_task()
            for agent in self.possible_agents:
                helicopter = self.helicopters_copy[self.agent_name_mapping[agent]]
                if self.check_helicopter(task, helicopter):
                    self.agents.append(agent)
            if self.agents == []:
                continue
            self._agent_selector.reinit(self.agents)
            flag = False

    def back_tracking(self, helicopter):
        deliver = helicopter.material_remain + helicopter.seat_remain + helicopter.medical_remain
        if (helicopter.busy_duration > 10 and helicopter.position_number != 0) or deliver == 0:
            self.positions_copy[helicopter.position_number].helicopters.remove(helicopter.number)
            self.positions_copy[0].helicopters.append(helicopter.number)

            helicopter.busy_duration = -self.edge_copy[helicopter.position_number][0] // helicopter.v * 60
            helicopter.position_number = 0

            n = helicopter.number
            helicopter.search_capacity = deepcopy(self.helicopters[n].search_capacity)
            helicopter.material_remain = deepcopy(self.helicopters[n].material_remain)
            helicopter.seat_remain = deepcopy(self.helicopters[n].seat_remain)
            helicopter.medical_remain = deepcopy(self.helicopters[n].medical_remain)
            helicopter.voyage_remain = deepcopy(self.helicopters[n].voyage_remain)

    def delete_0_value_task(self, task):
        i = 0
        for heli in self.helicopters_copy:
            distance = self.edge_copy[heli.position_number][task.position_number]
            t_time = distance * 60 // heli.v
            time_satisfaction = task.calculate_satisfaction(self.clock + t_time)
            if time_satisfaction == 0:
                i += 1
        if i == len(self.helicopters_copy):
            self.tasks_copy.remove(task)
            self.t_list.remove(task)
            self.positions_copy[task.position_number].tasks.remove(task.number)
            self.positions_copy[task.position_number].tasks.append(0.0 - task.number)
            self.satisfaction_list.append(0)
            self.delete_task.append(task.number)
            for agent in self.possible_agents:
                self.rewards[agent] = self.rewards[agent] - 10
            return False
        return True

    def check_helicopter(self, task, helicopter):
        search, material, person, medical = self.calculate(task, helicopter)
        if self.positions_copy[task.position_number].is_landed + helicopter.can_hover == 0:
            pass
        elif self.edge_copy[task.position_number][helicopter.position_number] + self.edge_copy[task.position_number][0] > helicopter.voyage_remain:
            pass
        elif search + material + person + medical == 0:
            pass
        elif helicopter.position_number == task.position_number:
            return True
        else:
            if helicopter.busy_duration < 0:
                return False
            else:
                return True
        return False 