# 新版本(v13) 直升机任务分配多智能体强化学习 - 使用说明

## 1. 项目简介
本目录整理了基于 PettingZoo 的多智能体仿真环境以及强化学习/启发式策略评估脚本，用于研究“多直升机应急任务分配”问题。在 v13 新版本中，代码已迁移至 gymnasium / PettingZoo 新 API，并提供：
- 多智能体环境（位置为智能体、直升机为智能体两类建模）
- 两个启发式基线策略（Nearby/Best-fit）
- 强化学习（PPO）最小训练示例与多策略评估脚本
- 预训练策略权重（基础 RL 与标注为 RLHF/Reward-Tuned 的权重包）

## 2. 目录结构
- aerta_position_v13_new.py：位置为智能体的 PettingZoo AEC 环境（主环境）
- aerta_helicopter_nearby_free_v13.py：直升机为智能体，临近策略环境
- aerta_helicopter_bestfit_free_v13.py：直升机为智能体，最优匹配环境
- train_v13_simple.py：最小化 API 验证与简化训练（PPO）脚本
- evaluate_all_strategies_new.py：生成任务集并对比评估 Nearby/Best-fit/RL 多种策略
- requirements_new.txt：新版本依赖（Python 3.9+）
- MIGRATION_GUIDE.md：从旧版到新栈的迁移指南
- 新版本运行状态分析报告.md：环境状态与迁移完成度分析
- nohf_7685_15e6.zip：基础 RL 策略（SB3 模型权重）
- rlhf_7685_15e6_7685_1e6_v3_394.zip：标注为“LLM-Tuned RL”（RLHF）策略权重
- rlhf_7685_15e6_7685_5e6_v3_400.zip：标注为“Reward-Tuned RL”（RLHF）策略权重

## 3. 环境要求
- Python 3.9+
- 推荐使用虚拟环境（venv / conda）
- CPU 可运行；如需 GPU，请安装匹配 CUDA 的 torch 版本

依赖安装（基础栈）：
- pip install -r requirements_new.txt
- 额外依赖：Supersuit（训练脚本所需）
  - pip install supersuit

注意：gym/gymnasium 不可混装；本项目使用 gymnasium + PettingZoo（返回五元组）。

## 4. 快速开始
1) 创建并激活虚拟环境（示例：venv）
- python3 -m venv .venv
- source .venv/bin/activate  # Windows: .venv\Scripts\activate

2) 安装依赖
- pip install -r requirements_new.txt
- pip install supersuit

3) 运行最小 API 测试与轻量训练
- python train_v13_simple.py
  - 执行流程：
    - test_new_api()：验证新 API（reset/step 返回格式等）
    - simple_train()：最小化 PPO 训练与保存（./test_model_v13.zip）

4) 运行多策略评估
- python evaluate_all_strategies_new.py
  - 将自动生成多轮任务集，评估 Nearby / Best-fit / RL（加载同目录下 zip 权重）
  - 所有输出重定向到 evaluation_results_YYYY-MM-DD_HH-MM-SS.txt

## 5. 核心建模与指标
- 实体：Position（是否可降落）、Helicopter（能力/资源/航程/速度/忙闲）、Task（时窗参数 m=[tk1,tk2,tkmax,rk0] 与多维需求）
- 目标：最大化总效益（时间满意度 × 实际完成量）
- 环境：PettingZoo AEC；aerta_position_v13_new 为主环境（位置为智能体），另提供两类直升机为智能体的启发式环境
- 指标：最终总效益、失败任务数/占比、与最佳效益差距（在评估脚本中统计）

## 6. 训练与模型
- 简化训练示例（train_v13_simple.py）：使用 Supersuit 将 PettingZoo 环境转换为 SB3 可训练形式，进行短步数 PPO 训练和模型保存
- 预训练权重：本目录包含基础 RL（nohf_*.zip）与两份“RLHF 标注”的模型（rlhf_*.zip）。评估脚本会按名称加载

注意：若需大规模训练，请调整 simple_train 中的 n_steps、batch_size、总步数等参数，并关注 SB3 v2.x 与旧版本默认超参差异（见 MIGRATION_GUIDE.md）。

## 7. 评估脚本用法（evaluate_all_strategies_new.py）
- 可通过修改以下变量调整评估规模：
  - num_eval_runs：评估轮数（默认 10）
  - base_seed：随机种子基数
  - generate_base_data(...)：每轮任务集大小/密度/时间跨度
- RL 模型集合在 rl_models_to_evaluate 字典中声明（名称→权重文件名，无需扩展名，SB3 会从 .zip 加载）
- 输出：逐轮效益、差距统计、失败任务数、均值/标准差等，总结写入日志文件

## 8. 常见问题排查（FAQ）
- ImportError: No module named 'supersuit'
  - 运行 pip install supersuit
- TypeError: step()/reset() 返回值数量不匹配
  - 确认只安装 gymnasium 而非 gym，PettingZoo 新版期望 5 元组（obs, reward, terminated, truncated, info）
- torch 安装失败或无 GPU
  - 先用 CPU 版本（requirements_new.txt），如需 GPU，请参考 PyTorch 官方案例选择匹配 CUDA 的 whl
- 渲染/打印结果为空
  - 简化示例中渲染仅输出关键统计，非图形化可视化；评估脚本将累计效益并写日志
- Windows/中文路径
  - 日志/图像保存路径如包含中文或反斜杠，请确保编码一致；本目录默认仅输出 txt 日志

## 9. 迁移说明（简要）
- gym → gymnasium；render_mode 需显式传入
- reset(seed=None, options=None)；step 返回五元组
- SB3 v2.x 默认超参与旧版存在差异（如 train_freq、batch_size 等）
- 详见 MIGRATION_GUIDE.md

## 10. RLHF / LLM 相关状态说明
- 仓库内可见的“RLHF/LLM”线索：
  - reward_model.py：偏好/奖励模型训练脚本（使用 feedback_data08.json），可产出 reward_model_*.pth
  - 1_aerta_test_rlhf_v2.py：旧版环境的训练/微调脚本，包含对 reward_model 与 LLM 接口（api.run_llm_local）的引用
  - 1_aerta_test_rlhf_v13.py：新版本对应文件，保留了 finetune 占位逻辑（如无 finetune 则回退为常规 learn）
  - 评估脚本使用“LLM-Tuned RL/Reward-Tuned RL”的命名加载已训练权重（rlhf_*.zip），用于对比评估
- 新增：
  - new_version_v13/api.py：提供 OpenAI 兼容接口 run_llm_local(prompt, ...)，需设置 OPENAI_API_KEY（可选 OPENAI_BASE_URL/OPENAI_MODEL）
  - new_version_v13/rm_reward_wrapper.py：示例性 RM 奖励融合 wrapper，r_total = r_env + λ · r_rm
  - new_version_v13/train_v13_with_rm.py：基于 wrapper 的最小训练示例
- 结论：
  - 评估层面可直接使用 rlhf_*.zip 权重；若要复现实验级 RLHF 训练，需确保 RM 特征工程与 reward_model.py 严格对齐，并根据需要接入 LLM 接口。

## 11. 可复现实验建议
- 固定随机种子（evaluate_all_strategies_new.py 中的 base_seed 与生成器内部 seed）
- 记录并锁定依赖版本（requirements_new.txt）；如可能，使用 pip-compile 生成锁定文件
- 对关键脚本添加 --help/参数化，保存运行命令与日志文件

## 12. 后续工作路线图（建议）
- 依赖统一：在 requirements_new.txt 中加入 supersuit，并最小化/锁版本
- 文档完善：拆分 README 的“训练/评估/数据生成”到独立文档
- RLHF 训练复现：
  - 将 reward_model 推理封装进 SB3 训练流程（环境奖励 + λ·RM 奖励）
  - 实现/替代 finetune 流程；补充 api.py 或移除对 LLM 的硬依赖
- 工程化：
  - 为环境与策略添加基础单测与快速 smoke test
  - 将大文件（*.zip）改用 Git LFS/外链

---
如需我直接补充 supersuit 到 requirements_new.txt、完善 RLHF 奖励整合示例或提供复现实验脚本，请告知你的具体优先级与目标。
