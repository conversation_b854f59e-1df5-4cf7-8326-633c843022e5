# 简化的PPO训练脚本 - 迁移到新版本API
from aerta_position_v13_new import env as aerta_position_v13_env, Position, Helicopter, Task
from stable_baselines3 import PPO
import supersuit as ss
from pettingzoo.utils import to_parallel
import numpy as np
import random

# 简化的测试数据
positions = [Position(0, "萧山机场", 1), Position(1, "杭州市富阳区", 1)]
helicopters = [Helicopter(1, 'EC145', 0, 1, 0, 1793, 3, 2, 680, 246, 0)]
edge = [[0, 57], [57, 0]]
positions[0].helicopters = [1]

def generate_simple_tasks():
    """生成简单的测试任务"""
    tasks = []
    for i in range(5):
        task = Task(i+1, i+1, 0, 0, 1, 1, 0, 0, 0, [10, 20, 30, 0.5])
        tasks.append(task)
    return tasks

def test_new_api():
    """测试新版本API"""
    print("测试新版本API...")
    tasks = generate_simple_tasks()
    
    # 创建环境
    env = aerta_position_v13_env(positions=positions, helicopters=helicopters, tasks=tasks, edge=edge)
    
    # 测试reset方法 - 新版本返回(observations, infos)
    observations, infos = env.reset()
    print(f"Reset成功，观察空间: {type(observations)}")
    
    # 测试step方法 - 新版本返回5个值
    step_count = 0
    for agent in env.agent_iter():
        obs, rew, terminated, truncated, info = env.last()
        done = terminated or truncated
        
        if done:
            print(f"环境结束，总步数: {step_count}")
            break
            
        # 随机动作
        action = env.action_space(agent).sample()
        env.step(action)
        step_count += 1
        
        if step_count > 20:  # 防止无限循环
            break
    
    env.close()
    print("API测试完成！")

def simple_train():
    """简单的训练测试"""
    print("开始简单训练测试...")
    tasks = generate_simple_tasks()
    
    # 创建环境
    env = aerta_position_v13_env(positions=positions, helicopters=helicopters, tasks=tasks, edge=edge)
    env = to_parallel(env)
    env = ss.pad_action_space_v0(env)
    env = ss.pettingzoo_env_to_vec_env_v1(env)
    env = ss.concat_vec_envs_v1(env, 1, base_class='stable_baselines3')
    
    # 创建PPO模型
    model = PPO("MlpPolicy", env, verbose=1, n_steps=64, batch_size=32)
    
    # 短时间训练测试
    print("开始训练...")
    model.learn(total_timesteps=1000)
    print("训练完成！")
    
    # 保存模型
    model.save("./test_model_v13")
    print("模型已保存")
    
    env.close()

if __name__ == '__main__':
    print("=== 新版本API迁移测试 ===")
    
    try:
        test_new_api()
        print("\n=== API测试成功 ===\n")
        
        simple_train()
        print("\n=== 训练测试成功 ===")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc() 