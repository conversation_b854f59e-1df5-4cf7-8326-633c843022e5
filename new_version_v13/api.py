"""
OpenAI 兼容 LLM 接口（预留）
- 依赖：openai>=1.0.0（已写入 requirements_new.txt）
- 用法：
    from api import run_llm_local
    text = run_llm_local(prompt)
- 注意：需要设置环境变量 OPENAI_API_KEY；如使用兼容服务，设置 OPENAI_BASE_URL。
"""
from __future__ import annotations
import os
from typing import Optional, Dict, Any

try:
    from openai import OpenAI
except Exception as e:  # 兜底提示，避免导入失败中断其他功能
    OpenAI = None  # type: ignore


_DEFAULT_MODEL = os.getenv("OPENAI_MODEL", "gpt-4o-mini")


def _make_client() -> OpenAI:
    if OpenAI is None:
        raise RuntimeError("openai 包未安装，请先 pip install openai>=1.0.0")
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise RuntimeError("缺少 OPENAI_API_KEY 环境变量")
    base_url = os.getenv("OPENAI_BASE_URL")  # 兼容自托管/代理服务
    if base_url:
        return OpenAI(api_key=api_key, base_url=base_url)
    return OpenAI(api_key=api_key)


def run_llm_local(prompt: str, *, model: Optional[str] = None, temperature: float = 0.2,
                  max_tokens: int = 256, extra: Optional[Dict[str, Any]] = None) -> str:
    """
    发送一个简单的 ChatCompletions 请求（对标 OpenAI 1.x SDK）。
    - prompt: 纯文本提示词
    - model: 覆盖默认模型名（默认从 OPENAI_MODEL 读取或 gpt-4o-mini）
    - temperature, max_tokens: 生成控制
    - extra: 透传给 SDK 的其他参数

    返回值：模型生成的文本
    """
    client = _make_client()
    mdl = model or _DEFAULT_MODEL
    kwargs = extra.copy() if extra else {}
    resp = client.chat.completions.create(
        model=mdl,
        messages=[{"role": "user", "content": prompt}],
        temperature=temperature,
        max_tokens=max_tokens,
        **kwargs,
    )
    # 兼容最常见的输出结构
    return resp.choices[0].message.content or ""

