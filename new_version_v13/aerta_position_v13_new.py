from copy import deepcopy
import heapq
import numpy as np 
import gymnasium as gym
from gymnasium import spaces
from gymnasium.utils import seeding
from pettingzoo import AECEnv
from pettingzoo.utils import agent_selector, wrappers
import math

inf = 10000

# **kwargs 就是接收你传给函数的 key=value 形式的参数,它会把所有这些参数打包成一个字典。
def env(**kwargs):
    env = raw_env(**kwargs)
    env = wrappers.CaptureStdoutWrapper(env)
    env = wrappers.OrderEnforcingWrapper(env)
    return env

class Position:
    def __init__(self, number, name, is_landed):
        self.number = number
        self.name = name
        self.is_landed = is_landed
        self.helicopters = []
        self.tasks = []
    
    def __eq__(self, other):
        return self.number == other.number

class Helicopter:
    def __init__(self, No, Name, position_number, can_hover, search_capacity, 
                 material_remain, seat_remain, medical_remain, voyage_remain, v, busy_duration):
        self.number = No
        self.Name = Name
        self.position_number = position_number
        self.can_hover = can_hover
        self.search_capacity = search_capacity
        self.material_remain = material_remain
        self.seat_remain = seat_remain
        self.medical_remain = medical_remain
        self.voyage_remain = voyage_remain
        self.v = v
        self.busy_duration = busy_duration
    
    def __eq__(self, other):
        return self.number == other.number

class Task:
    def __init__(self, No, publish_clock, tw, td, position_number, search_demand, 
                 material_demand, person_deliver, medical_demand, m):
        self.number = No
        self.publish_clock = publish_clock
        self.tw = tw
        self.td = td
        self.position_number = position_number
        self.search_demand = search_demand
        self.material_demand = material_demand
        self.person_deliver = person_deliver
        self.medical_demand = medical_demand
        self.m = m
        self.priority = self.calculate_residual_priority(0)
        self.finish_clock = -1
        self.finisher = ''
    
    def __lt__(self, other):
        return self.publish_clock < other.publish_clock
    
    def calculate_residual_priority(self, t):
        tk1 = self.m[0]
        tk2 = self.m[1]
        tkmax = self.m[2]
        rk0 = self.m[3]
        t = t - self.publish_clock
        if t <= tk1:
            f = -0.0001
        elif t < tk2:
            f = (2*t - 2*tk2) / (math.pow((tk2 - tk1), 2)) * (1 - rk0)
        elif t < tkmax:
            f = (2*tk2 - 2*t) / (math.pow((tkmax - tk2), 2)) * rk0
        else:
            f = inf
        demand = self.search_demand + self.material_demand/100 + self.person_deliver + self.medical_demand
        return f * demand
    
    def calculate_satisfaction(self, t):
        tk1 = self.m[0]
        tk2 = self.m[1]
        tkmax = self.m[2]
        rk0 = self.m[3]
        t = t - self.publish_clock
        if t <= tk1:
            f = 1
        elif t <= tk2:
            f = math.pow((tk2 - t) / (tk2 - tk1), 2) * (1 - rk0) + rk0
        elif t <= tkmax:
            f = -math.pow((t - tk2) / (tkmax - tk2), 2) * rk0 + rk0
        else:
            f = 0
        return f

class raw_env(AECEnv):
    metadata = {
        'render_modes': ['human'],  # 更新为新版本格式
        'name': "aerta_position_v13", 
        "is_parallelizable": True
    }

    def __init__(self, positions, helicopters, tasks, edge, render_mode=None):
        super().__init__()
        
        self.positions = positions
        self.helicopters = helicopters
        self.tasks = tasks
        self.edge = edge
        self.clock = 0
        self.render_mode = render_mode  # 新版本需要的参数

        self.tasks_copy = deepcopy(self.tasks)
        self.helicopters_copy = deepcopy(self.helicopters)
        self.positions_copy = deepcopy(self.positions)
        self.edge_copy = deepcopy(self.edge)
        
        self.possible_agents = ['position_' + str(positions[i].number) for i in range(len(positions))]      
        self.agent_name_mapping = dict(zip(self.possible_agents, list(range(len(positions)))))
        
        # 更新为新版本的spaces定义
        self.action_spaces = {
            agent: spaces.Discrete(len(self.helicopters) + 1) 
            for agent in self.possible_agents
        }
        
        # 更新观察空间定义
        ob = spaces.Box(
            low=0, 
            high=600, 
            shape=(2, len(self.helicopters) + 1, 7), 
            dtype=np.int32
        )
        self.observation_spaces = {agent: ob for agent in self.possible_agents}

        self.seed()

        self.agents = self.possible_agents[:]
        self._agent_selector = agent_selector(self.agents)
        self.rewards = {agent: 0 for agent in self.possible_agents}
        self._cumulative_rewards = {agent: 0 for agent in self.possible_agents}
        self.dones = {agent: False for agent in self.possible_agents}
        self.infos = {agent: {} for agent in self.possible_agents}

        self.circle = 0
        self.reject = {task: False for task in self.tasks_copy}
        
        self.delete_task = []
        self.value = 0
        self.satisfaction_list = []
        
    def observation_space(self, agent):
        return self.observation_spaces[agent]

    def action_space(self, agent):
        return self.action_spaces[agent]

    def seed(self, seed=None):
        # 更新为新版本的seeding方式
        if seed is None:
            seed = np.random.randint(0, 2**32 - 1)
        self.np_random = np.random.RandomState(seed)
        return [seed]

    def close(self):
        pass

    def _convert_to_dict(self, list_of_list):
        return dict(zip(self.possible_agents, list_of_list))

    def reset(self, seed=None, options=None):
        # 更新reset方法签名以符合新版本
        if seed is not None:
            self.seed(seed)
            
        self.has_reset = True

        self.tasks_copy = deepcopy(self.tasks)
        self.helicopters_copy = deepcopy(self.helicopters)
        self.positions_copy = deepcopy(self.positions)
        self.edge_copy = deepcopy(self.edge)
        self.clock = 0

        self.agents = self.possible_agents[:]
        self._agent_selector.reinit(self.agents)
        self.agent_selection = self._agent_selector.reset()
        self.rewards = self._convert_to_dict(np.zeros(len(self.possible_agents)))
        self._cumulative_rewards = self._convert_to_dict(np.zeros(len(self.possible_agents)))
        self.dones = self._convert_to_dict([False for _ in range(len(self.possible_agents))])
        self.infos = self._convert_to_dict([{} for _ in range(len(self.possible_agents))])
        
        self.circle = 0
        self.reject = {task: False for task in self.tasks_copy}
        
        self.delete_task = []
        self.value = 0
        self.satisfaction_list = []
        
        # 新版本需要返回观察和信息
        observations = {agent: self.observe(agent) for agent in self.agents}
        infos = {agent: {} for agent in self.agents}
        return observations, infos
    
    def render(self, mode=None):
        # 使用实例的render_mode或传入的mode
        mode = mode or self.render_mode or "human"
        
        r = []
        if len(self.tasks_copy) != 0:
            pass
        else:
            if mode == "human":
                print("Clock:{}-----------------".format(self.clock))
                for agent in self.possible_agents:
                    helis = []
                    ta_2 = []
                    i = self.agent_name_mapping[agent]
                    if self.positions_copy[i].helicopters != []:
                        for number in self.positions_copy[i].helicopters:
                            heli = self.helicopters_copy[number-1]
                            helis.append("{}[{},{},{},{}]/{}".format(
                                heli.Name, heli.search_capacity, heli.material_remain, 
                                heli.seat_remain, heli.medical_remain, heli.busy_duration))
                    if self.positions_copy[i].tasks != []:
                        for number in self.positions_copy[i].tasks:
                            if number < 0:
                                ta_2.append(number)
                print("all tasks have done -----------------")
                print("total value:{}".format(self.value))
                
                if self.satisfaction_list:
                    sum_satisfaction = sum(self.satisfaction_list)
                    aver = sum_satisfaction / len(self.satisfaction_list)
                    e = sum(math.pow((i - aver), 2) for i in self.satisfaction_list)
                    aver_e = e / len(self.satisfaction_list)
                    print("the average time satisfaction is {}".format(aver))
        
        return self.value, len(self.delete_task)

    def calculate(self, task, helicopter):
        search = min(task.search_demand, helicopter.search_capacity)
        material = min(task.material_demand, helicopter.material_remain)
        person = min(task.person_deliver, helicopter.seat_remain)
        medical = min(task.medical_demand, helicopter.medical_remain)
        return search, material, person, medical

    def calculate_time(self, task, helicopter):
        search, material, person, medical = self.calculate(task, helicopter)
        transport_time = self.edge_copy[helicopter.position_number][task.position_number] * 60 // helicopter.v
        if helicopter.busy_duration < 0:
            transport_time += -helicopter.busy_duration
        deliver_time = search * 5 + material // 100 + person * 5 + medical * 10
        return transport_time + deliver_time

    def observe(self, agent):
        self.obs=[[[0 for i in range(7)]for j in range(len(self.helicopters_copy)+1)]for k in range(2)]
        observation=[[[0 for i in range(7)]for j in range(len(self.helicopters_copy)+1)]for k in range(2)]
        self._clear_rewards()
        next_task=self.get_next_task()

        # generate obs
        if next_task==None:
            return observation
        position=self.positions_copy[self.agent_name_mapping[agent]]
        if position.helicopters!=[]:
            i=1
            for number in position.helicopters:
                heli=self.helicopters_copy[number-1]
                if self.check_helicopter(next_task,position,heli)!=0:
                    observation[0][i][0]=heli.number
                    if next_task.search_demand<=heli.search_capacity :
                        observation[0][i][1]=1
                    if next_task.material_demand<=heli.material_remain :
                        observation[0][i][2]=1
                    if next_task.person_deliver<=heli.seat_remain :
                        observation[0][i][3]=1
                    if next_task.medical_demand<=heli.medical_remain :
                        observation[0][i][4]=1
                    search,material,person,medical=self.calculate(next_task,heli)
                    observation[0][i][5]=search+material//100+person+medical
                    if heli.position_number==next_task.position_number:
                        observation[0][i][6]=-heli.busy_duration
                    else:
                        observation[0][i][6]=self.edge_copy[heli.position_number][next_task.position_number]*60//heli.v
                    i+=1
        next_t_list=heapq.nsmallest(len(self.helicopters_copy)+1,self.t_list,key=lambda s: s.priority)
        for i in range(len(next_t_list)):
            if i==0:
                continue
            observation[1][i][1]=next_t_list[i].search_demand
            observation[1][i][2]=next_t_list[i].material_demand
            observation[1][i][3]=next_t_list[i].person_deliver
            observation[1][i][4]=next_t_list[i].medical_demand
            observation[1][i][6]=self.edge_copy[position.number][next_task.position_number]
        self.obs=np.array(observation,dtype='int32')

        # change infos
        helis=[]
        ta_0=[]
        ta_1=[]
        if len(self.tasks_copy)!=0:
            for heli in self.helicopters_copy:
                helis.append([heli.number,heli.Name,self.positions_copy[heli.position_number].number,heli.can_hover,heli.search_capacity,heli.material_remain,heli.seat_remain,heli.medical_remain,heli.voyage_remain,heli.v,heli.busy_duration,heli.position_number])
            for agent in self.possible_agents:
                i=self.agent_name_mapping[agent]
                if self.positions_copy[i].tasks!=[]:
                    for number in self.positions_copy[i].tasks:
                        if number<0:
                            ta_0.append([-number,self.positions_copy[i].number])
                        else:
                            for ta in self.tasks_copy:
                                if ta.number==number:
                                    ta_1.append([ta.number,self.positions_copy[i].number,self.positions_copy[ta.position_number].is_landed,ta.search_demand,ta.material_demand,ta.person_deliver,ta.medical_demand,ta.calculate_satisfaction(self.clock)])
                                    break

        helis=heapq.nsmallest(len(helis),helis,key=lambda s: s[0])
        ta_0=heapq.nsmallest(len(ta_0),ta_0,key=lambda s: s[0])
        if ta_1!=[]:
            ta_1=heapq.nsmallest(len(ta_1),ta_1,key=lambda s: s[0])
        if next_task is None:
            task_r=0
        else:
            task_r=next_task.number
        self.infos={ag:{'myself':[helis,ta_0,ta_1,task_r]} for ag in self.possible_agents}
    
        return np.array(observation,dtype='int32')

    def distribute_reward(self, helicopter, task):
        t_time = self.calculate_time(task, helicopter)
        time_satisfaction = task.calculate_satisfaction(self.clock + t_time)
        self.satisfaction_list.append(time_satisfaction)
        search, material, person, medical = self.calculate(task, helicopter)
        deliver = search + material/100 + person + medical
        return time_satisfaction * deliver

    def step(self, action):
        agent = self.agent_selection
        if self.dones[self.agent_selection]:
            return self._was_done_step(action)
        if len(self.tasks_copy)!=0:
            self.task=self.get_next_task()
            if len(self.tasks_copy)==0:
                if self._agent_selector.is_last():
                    self.dones = {agent: True for agent in self.agents}
                self.agent_selection = self._agent_selector.next()
                return None
            position=self.positions_copy[self.agent_name_mapping[agent]]
            check=self.obs[0][action][0]
            if check!=0:
                helicopter=self.helicopters_copy[check-1]
                rew=self.distribute_reward(helicopter,self.task)
                self.rewards = {ag: rew for ag in self.possible_agents}
                self.value+=rew
                self.change_state(helicopter,self.task,position)
                self.reject={task:False for task in self.tasks_copy}
                self.update_state()
            else:
                self.circle+=1
                if self.circle==len(self.positions_copy):
                    self.reject[self.task]=True
                    self.update_state()
        else:
            if self._agent_selector.is_last():
                self.dones = {agent: True for agent in self.agents}
        self.agent_selection = self._agent_selector.next()
        self._cumulative_rewards[agent]=0
        self._accumulate_rewards()
        
        # 新版本返回格式：observation, reward, terminated, truncated, info
        return (self.obs, self.rewards[agent], self.dones[agent], False, self.infos[agent])

    def get_next_task(self):
        self.t_list=[]
        for i in range(len(self.tasks_copy)):
            if self.tasks_copy[i].publish_clock <= self.clock:
                if self.reject[self.tasks_copy[i]]==False:
                    self.t_list.append(self.tasks_copy[i])
                if self.tasks_copy[i].number not in self.positions_copy[self.tasks_copy[i].position_number].tasks:
                    self.positions_copy[self.tasks_copy[i].position_number].tasks.append(self.tasks_copy[i].number)
        for task in self.t_list:
            task.priority=task.calculate_residual_priority(self.clock)
            self.delete_0_value_task(task)
        next_t_list=heapq.nsmallest(1,self.t_list,key=lambda s: s.priority)
        if len(next_t_list)>0:
            return next_t_list[0]
        return None

    def change_state(self, helicopter, task, position):
        search, material, person, medical = self.calculate(task, helicopter)
        
        helicopter.busy_duration = -self.calculate_time(task, helicopter)
        helicopter.material_remain = helicopter.material_remain - material
        helicopter.seat_remain = helicopter.seat_remain - person
        helicopter.medical_remain = helicopter.medical_remain - medical
        helicopter.voyage_remain = helicopter.voyage_remain - self.edge_copy[helicopter.position_number][task.position_number]
        helicopter.position_number = task.position_number
        
        task.td = -helicopter.busy_duration
        task.tw = self.clock - task.publish_clock
        task.search_demand = task.search_demand - search
        task.material_demand = task.material_demand - material
        task.person_deliver = task.person_deliver - person
        task.medical_demand = task.medical_demand - medical
        if task.search_demand + task.material_demand + task.person_deliver + task.medical_demand == 0:
            self.tasks_copy.remove(task)
            self.positions_copy[task.position_number].tasks.remove(task.number)
            self.positions_copy[task.position_number].tasks.append(0 - task.number)
        
        position.helicopters.remove(helicopter.number)
        self.positions_copy[task.position_number].helicopters.append(helicopter.number)

    def update_state(self):
        self.circle = 0
        flag = True
        while flag:
            if len(self.tasks_copy) == 0:
                return 0
            self.clock += 1
            busy_helicopters = 0
            for helicopter in self.helicopters_copy:
                helicopter.busy_duration += 1
                self.back_tracking(helicopter)
                if helicopter.busy_duration < 0:
                    busy_helicopters += 1
                if helicopter.busy_duration == 0:
                    helicopter.seat_remain = deepcopy(self.helicopters[helicopter.number-1].seat_remain)
                    helicopter.medical_remain = deepcopy(self.helicopters[helicopter.number-1].medical_remain)
            if busy_helicopters == len(self.helicopters_copy):
                continue

            self.task = self.get_next_task()
            if self.task == None:
                self.reject = {task: False for task in self.tasks_copy}
                continue
            
            flag = False

    def back_tracking(self, helicopter):
        deliver = helicopter.material_remain + helicopter.seat_remain + helicopter.medical_remain
        if (helicopter.busy_duration > 10 and helicopter.position_number != 0) or deliver == 0:
            self.positions_copy[helicopter.position_number].helicopters.remove(helicopter.number)
            self.positions_copy[0].helicopters.append(helicopter.number)

            helicopter.busy_duration = -self.edge_copy[helicopter.position_number][0] // helicopter.v * 60
            helicopter.position_number = 0

            n = helicopter.number
            helicopter.search_capacity = deepcopy(self.helicopters[n-1].search_capacity)
            helicopter.material_remain = deepcopy(self.helicopters[n-1].material_remain)
            helicopter.seat_remain = deepcopy(self.helicopters[n-1].seat_remain)
            helicopter.medical_remain = deepcopy(self.helicopters[n-1].medical_remain)
            helicopter.voyage_remain = deepcopy(self.helicopters[n-1].voyage_remain)
            
    def delete_0_value_task(self, task):
        i = 0
        for heli in self.helicopters_copy:
            distance = self.edge_copy[heli.position_number][task.position_number]
            t_time = distance * 60 // heli.v
            time_satisfaction = task.calculate_satisfaction(self.clock + t_time)
            if time_satisfaction == 0:
                i += 1
        if i == len(self.helicopters_copy):
            self.tasks_copy.remove(task)
            self.t_list.remove(task)
            self.positions_copy[task.position_number].tasks.remove(task.number)
            self.positions_copy[task.position_number].tasks.append(0.0 - task.number)
            self.satisfaction_list.append(0)
            self.delete_task.append(task.number)
            for agent in self.possible_agents:
                self.rewards[agent] = self.rewards[agent] - 10
            return False
        return True

    def check_helicopter(self, task, position, helicopter):
        search, material, person, medical = self.calculate(task, helicopter)
        if self.positions_copy[task.position_number].is_landed + helicopter.can_hover == 0:
            pass
        elif self.edge_copy[task.position_number][helicopter.position_number] + self.edge_copy[task.position_number][0] > helicopter.voyage_remain:
            pass
        elif search + material + person + medical == 0:
            pass
        elif helicopter.position_number != position.number:
            pass
        elif helicopter.position_number == task.position_number:
            return True
        else:
            if helicopter.busy_duration < 0:
                return False
            else:
                return True
        return False 