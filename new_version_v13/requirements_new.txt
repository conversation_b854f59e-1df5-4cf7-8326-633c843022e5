# 新版本依赖文件 - 适用于Python 3.9+
# 基于代码迁移项目需求

# 核心强化学习库
gymnasium>=0.29.1
pettingzoo>=1.24.0
stable-baselines3>=2.0.0

supersuit>=3.9.0
openai>=1.0.0

# 深度学习框架
torch>=2.3.0
torchvision>=0.18.0

# 数值计算和科学计算
numpy>=1.24.0,<2.0  # 兼容性考虑
scipy>=1.9.0
pandas>=1.5.0

# 可视化和监控
matplotlib>=3.6.0
tensorboard>=2.9.1
wandb>=0.13.0  # 可选的实验跟踪

# 环境和工具
opencv-python>=4.6.0  # 图像处理
pillow>=9.0.0  # 图像处理
tqdm>=4.64.0  # 进度条

# 测试和开发工具
pytest>=7.0.0
pytest-cov>=4.0.0
black>=22.0.0  # 代码格式化
flake8>=5.0.0  # 代码检查

# 可选依赖（根据需要安装）
# shimmy>=0.2.1  # Gym兼容性层，如果需要支持旧版Gym
# sb3-contrib>=2.0.0  # 额外算法
# rl-zoo3>=2.0.0  # 训练框架

# 系统依赖（可能需要）
psutil>=5.9.0  # 系统监控
cloudpickle>=2.2.0  # 序列化