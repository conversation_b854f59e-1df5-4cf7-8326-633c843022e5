from sched import scheduler
from matplotlib import pyplot as plt
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import json
from torch.nn.utils.rnn import pad_packed_sequence, pack_sequence, pad_sequence

def collate_fn(batch):
    contexts, responses1, labels = zip(*batch)
    context_tensors = []
    
    for context in contexts:
        combined_context = torch.cat([
            torch.tensor(context['obs'], dtype=torch.float32).view(-1),
            torch.tensor(context['infos0'], dtype=torch.float32).view(-1),
            torch.tensor(context['infos1'], dtype=torch.float32).view(-1),
            torch.tensor(context['infos2'], dtype=torch.float32).view(-1),
            torch.tensor(context['infos3'], dtype=torch.float32).view(-1)
        ])
        context_tensors.append(combined_context)
    
    # 确定最大长度
    # max_length = max(tensor.size(0) for tensor in context_tensors)
    max_length=898

    # 手动填充
    padded_context_tensors = []
    for tensor in context_tensors:
        padding_size = max_length - tensor.size(0)
        if padding_size > 0:
            # 添加填充
            padded_tensor = torch.nn.functional.pad(tensor, (0, padding_size), "constant", 0)
        else:
            padded_tensor = tensor
        padded_context_tensors.append(padded_tensor)
    context_tensor_batch = torch.stack(padded_context_tensors)
    # context_tensor_batch = torch.stack(context_tensors)

    responses1_tensor = torch.stack([torch.tensor(response1, dtype=torch.float32).view(-1) for response1 in responses1])

    labels_tensor = torch.tensor(labels, dtype=torch.long)
    
    return context_tensor_batch, responses1_tensor, labels_tensor
    # return packed_context_tensors, responses1_tensor, responses2_tensor, labels_tensor


class preferenceDataset(Dataset):
    def __init__(self, df):
        self.contexts = df['context'].apply(lambda x: json.loads(x)).values
        self.responses1 = df['response1'].apply(lambda x: torch.tensor(x, dtype=torch.long)).values
        self.labels = df['preference'].apply(lambda x: torch.tensor(x, dtype=torch.long)).values

    def __len__(self):
        return len(self.labels)

    def __getitem__(self, index):
        context = self.contexts[index]
        response1 = self.responses1[index]
        label = self.labels[index]

        context_tensor = {key: torch.tensor(value, dtype=torch.float32) for key, value in context.items()}
        return context_tensor, response1, label

class rewardModel(nn.Module):
    def __init__(self, context_size, response_size):
        super(rewardModel, self).__init__()
        # 调整神经元数量，减少模型复杂度
        self.context_fc = nn.Linear(context_size, 32)
        self.response_fc = nn.Linear(response_size, 32)
        self.combined_fc1 = nn.Linear(64, 32)
        self.combined_fc2 = nn.Linear(32, 16)
        self.combined_fc3 = nn.Linear(16, 1)    # 保持输出层不变

        self.dropout = nn.Dropout(0.4)  # 可以增加Dropout率以避免过拟合
        self.bn1 = nn.BatchNorm1d(32)
        self.bn2 = nn.BatchNorm1d(32)

        self.leaky_relu = nn.LeakyReLU()
        self.sigmoid = nn.Sigmoid()

    def forward(self, context, response):
        # 全连接层 -> BatchNorm -> LeakyReLU -> Dropout
        context_emb = self.leaky_relu(self.bn1(self.context_fc(context)))
        response_emb = self.leaky_relu(self.bn2(self.response_fc(response)))

        # 拼接context和response
        combined = torch.cat((context_emb, response_emb), dim=1)

        # 全连接层 -> LeakyReLU -> Dropout
        x = self.leaky_relu(self.combined_fc1(combined))
        x = self.dropout(x)

        # 全连接层 -> LeakyReLU -> Dropout
        x = self.leaky_relu(self.combined_fc2(x))
        x = self.dropout(x)

        # 输出层
        x = self.combined_fc3(x)
        x = self.sigmoid(x)

        return x

def train(model, train_loader, criterion, optimizer, test_loader, epochs=10, patience=20):
    best_val_loss = float('inf')
    early_stopping_counter = 0
    model.train()
    for epoch in range(epochs):
        running_loss = 0.0
        for context, response1, labels in train_loader:
            optimizer.zero_grad()
            output1 = model(context, response1)
            loss = criterion(output1.squeeze(), labels.float())
            loss.backward()
            optimizer.step()
            running_loss += loss.item()
        print(f'epoch [{epoch+1}/{epochs}], loss: {running_loss/len(train_loader)}')
        val_loss, val_accuracy = evaluate(model, train_loader, criterion)

        val_loss, _ = evaluate(model, test_loader, criterion)
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            early_stopping_counter = 0
            # 保存最佳模型
            torch.save(model, 'reward_model_sam.pth')
            torch.save(model.state_dict(), 'reward_model_state_dict_sam.pth')
        else:
            early_stopping_counter += 1
            if early_stopping_counter >= patience:
                print("Early stopping")
                break


def evaluate(model, test_loader, criterion):
    model.eval()
    running_val_loss = 0.0
    correct = 0
    total = 0
    with torch.no_grad():
        for context, response1, labels in test_loader:
            output1 = model(context, response1)
            val_loss = criterion(output1.squeeze(), labels.float())
            running_val_loss += val_loss.item()
            predictions = (output1 > 0.5).float()
            correct += (predictions.squeeze(1) == labels).sum().item()
            total += labels.size(0)
    avg_val_loss = running_val_loss / len(test_loader)
    accuracy = correct / total
    print(f'accuracy: {accuracy * 100:.2f}%, avg_val_loss: {avg_val_loss * 100:.2f}%')
    return avg_val_loss, accuracy

# 计算 context_size
def calculate_max_context_size(df):
    # 初始化最大值
    max_obs = 0
    max_infos0 = 0
    max_infos1 = 0
    max_infos2 = 0
    max_infos3 = 0
    
    # 遍历 DataFrame 中的所有样本
    for _, row in df.iterrows():
        context = json.loads(row['context'])
        
        # 更新最大值
        obs = context['obs']
        infos0 = context['infos0']
        infos1 = context['infos1']
        infos2 = context['infos2']
        
        max_obs = max(max_obs, np.prod(torch.tensor(obs, dtype=torch.float32).shape))
        max_infos0 = max(max_infos0, np.prod(torch.tensor(infos0, dtype=torch.float32).shape))
        max_infos1 = max(max_infos1, np.prod(torch.tensor(infos1, dtype=torch.float32).shape))
        max_infos2 = max(max_infos2, np.prod(torch.tensor(infos2, dtype=torch.float32).shape))
        max_infos3 = 1
    
    # 计算总的 context_size
    context_size = max_obs + max_infos0 + max_infos1 + max_infos2 + max_infos3
    return context_size


from torch.utils.data import Dataset, DataLoader, Subset
from sklearn.model_selection import train_test_split, KFold    
best_fold_model = None
best_fold_loss = float('inf')  # 初始化为正无穷大
best_fold_accuracy = 0
best_epoch = 0

def train_kfold(model, criterion, optimizer, train_loader, val_loader, epochs, patience, fold_idx, device):
    global best_fold_model, best_fold_loss, best_fold_accuracy, best_epoch
    best_val_loss = float('inf')
    best_e=0
    early_stopping_counter = 0
    model.to(device)  # 将模型移到指定设备（GPU 或 CPU）

    train_loss=[]
    val_lo=[]
    val_acc=[]
    epo=[]
    ti=0

    for epoch in range(epochs):
        model.train()
        running_loss = 0.0

        for context, response1, labels in train_loader:
            # 将数据移到设备（GPU 或 CPU）
            context, response1, labels = context.to(device), response1.to(device), labels.to(device)
            
            optimizer.zero_grad()
            output1 = model(context, response1)
            loss = criterion(output1.squeeze(), labels.float())
            loss.backward()
            optimizer.step()
            running_loss += loss.item()
        
        print(f'epoch [{epoch+1}/{epochs}], loss: {running_loss/len(train_loader)}')
        train_loss.append(running_loss/len(train_loader))
        
        # 验证阶段
        val_loss, val_accuracy = evaluate_1(model, val_loader, criterion, device)
        print(f'Fold {fold_idx}, epoch [{epoch+1}/{epochs}], val_loss: {val_loss}, val_accuracy: {val_accuracy * 100:.2f}%')
        val_lo.append(val_loss)
        val_acc.append(val_accuracy)
        epo.append(epoch+1)

        # 检查是否为最优模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            early_stopping_counter = 0
            # 保存当前最优模型
            torch.save(model.state_dict(), f'best_model_fold_{fold_idx}_sam.pth')
            best_e=epoch
        else:
            early_stopping_counter += 1
            if early_stopping_counter >= patience:
                print(f"Early stopping for fold {fold_idx}")
                break

    # 记录表现最佳的模型
    if best_val_loss < best_fold_loss:
        best_fold_loss = best_val_loss
        best_fold_model = model
        best_fold_accuracy = val_accuracy
        best_epoch=best_e
        torch.save(model.state_dict(), 'best_fold_model_sam.pth')  # 保存最佳折模型

    return train_loss,val_lo,val_acc,epo,best_fold_loss,best_fold_accuracy,best_epoch

def evaluate_1(model, loader, criterion, device):
    model.eval()
    running_val_loss = 0.0
    correct = 0
    total = 0
    with torch.no_grad():
        for context, response1, labels in loader:
            # 将数据移到设备（GPU 或 CPU）
            context, response1, labels = context.to(device), response1.to(device), labels.to(device)

            output1 = model(context, response1)
            # 使用 view(-1) 确保维度一致
            val_loss = criterion(output1.view(-1), labels.float())
            running_val_loss += val_loss.item()
            
            # 预测时也确保张量维度匹配
            predictions = (output1 > 0.5).float().view(-1)
            correct += (predictions == labels).sum().item()
            total += labels.size(0)
    
    avg_val_loss = running_val_loss / len(loader)
    accuracy = correct / total
    return avg_val_loss, accuracy

def test_kfold():
    # 从文件中读取数据并进行测试集保留
    with open('feedback_data08.json', 'r') as f:
        df = pd.DataFrame(json.load(f))

    # 留出20%作为测试集
    train_df, test_df = train_test_split(df, test_size=0.2, random_state=76)

    kfold = KFold(n_splits=5, shuffle=True, random_state=76)

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")  # 检查是否有可用的GPU

    fig, axs = plt.subplots(3, 1, figsize=(10, 18))
    gray_colors = ['0.1', '0.2', '0.3', '0.4', '0.5', '0.6', '0.7', '0.8', '0.9']
    line_styles = ['-', '--', '-.', ':']

    for fold, (train_idx, val_idx) in enumerate(kfold.split(train_df)):
        train_fold = train_df.iloc[train_idx]
        val_fold = train_df.iloc[val_idx]

        train_dataset = preferenceDataset(train_fold)
        val_dataset = preferenceDataset(val_fold)

        train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True, collate_fn=collate_fn)
        val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False, collate_fn=collate_fn)

        context_size = calculate_max_context_size(df)
        response_size = 1

        model = rewardModel(context_size, response_size).to(device)  # 将模型移到设备
        criterion = nn.BCELoss()
        optimizer = optim.AdamW(model.parameters(), lr=0.0001, weight_decay=1e-4)

        train_loss,val_loss,val_accuracy,epo,best_fold_loss,best_fold_accuracy,best_epoch = train_kfold(model, criterion, optimizer, train_loader, val_loader, epochs=100, patience=20, fold_idx=fold + 1, device=device)

        color = gray_colors[fold % len(gray_colors)]
        line_style = line_styles[fold % len(line_styles)]
        axs[0].plot(epo, train_loss, label=f'Fold {fold+1}', color=color, linestyle=line_style, markersize=5, linewidth=2)
        axs[1].plot(epo, val_loss, label=f'Fold {fold+1}',  color=color, linestyle=line_style, markersize=5, linewidth=2)
        axs[2].plot(epo, val_accuracy, label=f'Fold {fold+1}',  color=color, linestyle=line_style, markersize=5, linewidth=2)
    axs[1].scatter(best_epoch, best_fold_loss, color='black',marker='*',s=100)
    axs[2].scatter(best_epoch, best_fold_accuracy, color='black',marker='*',s=100)

    # 使用保留的测试集进行最终评估
    print("Evaluating the best model on the test set...")
    test_dataset = preferenceDataset(test_df)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False, collate_fn=collate_fn)

    best_model = rewardModel(context_size, response_size).to(device)
    best_model.load_state_dict(torch.load('best_fold_model_sam.pth'))  # 加载保存的最佳模型
    test_loss, test_accuracy = evaluate_1(best_model, test_loader, criterion, device=device)
    print(f'Test Loss: {test_loss}, Test Accuracy: {test_accuracy * 100:.2f}%')

    # 保存最终的最佳模型
    torch.save(best_model.state_dict(), 'final_best_model_sam.pth')

    # 添加图例和标签
    axs[0].legend(loc='upper right', fontsize=12)  
    axs[0].set_xlabel('epoch', fontsize=14)
    axs[0].set_ylabel('train_loss', fontsize=14)
    # axs[0].grid(True, linestyle=':', color='gray')
    # axs[0].xticks(fontsize=12)
    # axs[0].yticks(fontsize=12)
    axs[1].legend(loc='upper right',fontsize=12)  
    axs[1].set_xlabel('epoch', fontsize=14)
    axs[1].set_ylabel('validate_loss', fontsize=14)
    # axs[1].grid(True, linestyle=':', color='gray')
    # axs[1].xticks(fontsize=12)
    # axs[1].yticks(fontsize=12)
    axs[2].legend(loc='upper right',fontsize=12)  
    axs[2].set_xlabel('epoch', fontsize=14)
    axs[2].set_ylabel('validate_accuracy', fontsize=14)
    # axs[2].grid(True, linestyle=':', color='gray')
    # axs[2].xticks(fontsize=12)
    # axs[2].yticks(fontsize=12)

    # 显示图形
    plt.savefig("D:\onedrive\\1大论文\\2绘图\\奖励模型\\0rm_train_loss.tif")
    plt.show()

if __name__ == "__main__":
    test_kfold()
